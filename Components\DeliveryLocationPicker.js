import React, { useState, useEffect, useRef } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    ActivityIndicator,
    Alert,
    Dimensions,
    Platform,
    TextInput,
    KeyboardAvoidingView,
    Animated,
    ScrollView,
    Vibration
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import { isWithinDeliveryZone, isValidPincode, getPincodeMessage } from '../utils/locationUtils';
import MapView, { Marker, PROVIDER_GOOGLE } from 'react-native-maps';
import PincodeAlert from './PincodeAlert';

const { width, height } = Dimensions.get('window');
const ASPECT_RATIO = width / height;
const LATITUDE_DELTA = 0.004; // Adjusted delta for approximately 300-400m view
const LONGITUDE_DELTA = LATITUDE_DELTA * ASPECT_RATIO;

const DeliveryLocationPicker = ({
    onLocationSelected = () => {},
    onClose = () => {},
    onValidationChange = () => {},
    existingAddress = null // New prop for editing existing address
}) => {
    // State variables
    const [location, setLocation] = useState({
        latitude: existingAddress?.coordinates?.latitude || existingAddress?.latitude || 12.8997028,
        longitude: existingAddress?.coordinates?.longitude || existingAddress?.longitude || 79.136073
    });
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [isWithinZone, setIsWithinZone] = useState(true);
    const [showAddressForm, setShowAddressForm] = useState(false);
    const [showMap, setShowMap] = useState(true);
    const [addressDetails, setAddressDetails] = useState({
        flatNumber: existingAddress?.doorNo || '',
        street: existingAddress?.streetName || '',
        locality: existingAddress?.area || '',
        district: existingAddress?.district || 'Vellore',
        pincode: existingAddress?.pincode || '',
        addressType: existingAddress?.type || existingAddress?.addressType || 'Home'
    });

    // Field validation state
    const [fieldErrors, setFieldErrors] = useState({
        flatNumber: '',
        street: '',
        locality: '',
        district: '',
        pincode: ''
    });

    // Pincode validation state
    const [showPincodeAlert, setShowPincodeAlert] = useState(false);
    const [pincodeMessage, setPincodeMessage] = useState(null);
    const [canSaveAddress, setCanSaveAddress] = useState(true);

    // Refs
    const mapRef = useRef(null);
    const hasShownDeliveryZoneWarning = useRef(false);

    // Animation values for the pulsating effect
    const pulseAnim = useRef(new Animated.Value(1)).current;
    const pulseOpacity = useRef(new Animated.Value(0.7)).current;

    // Set up pulsating animation
    useEffect(() => {
        const startPulseAnimation = () => {
            Animated.parallel([
                Animated.sequence([
                    Animated.timing(pulseAnim, {
                        toValue: 1.8,
                        duration: 800,
                        useNativeDriver: true
                    }),
                    Animated.timing(pulseAnim, {
                        toValue: 1,
                        duration: 800,
                        useNativeDriver: true
                    })
                ]),
                Animated.sequence([
                    Animated.timing(pulseOpacity, {
                        toValue: 0.5,
                        duration: 800,
                        useNativeDriver: true
                    }),
                    Animated.timing(pulseOpacity, {
                        toValue: 1,
                        duration: 800,
                        useNativeDriver: true
                    })
                ])
            ]).start(() => {
                startPulseAnimation();
            });
        };

        startPulseAnimation();

        return () => {
            pulseAnim.stopAnimation();
            pulseOpacity.stopAnimation();
        };
    }, []);

    // If editing an existing address, automatically show the address form without map
    useEffect(() => {
        if (existingAddress) {
            // Immediately show the address form for editing without map interaction
            setShowAddressForm(true);
            // Hide the map completely when editing an address
            setShowMap(false);
        }
    }, [existingAddress]);

    // Zoom in when the map is first loaded
    useEffect(() => {
        // Immediate zoom without delay
        if (mapRef.current && !existingAddress) {
            // First immediate zoom
            mapRef.current.animateToRegion({
                ...location,
                latitudeDelta: 0.004, // Approximately 300-400m view
                longitudeDelta: 0.004 * ASPECT_RATIO
            }, 10);

            // Second zoom after a short delay to ensure it takes effect
            const timer = setTimeout(() => {
                if (mapRef.current) {
                    mapRef.current.animateToRegion({
                        ...location,
                        latitudeDelta: 0.004, // Approximately 300-400m view
                        longitudeDelta: 0.004 * ASPECT_RATIO
                    }, 500);
                }
            }, 300);

            return () => clearTimeout(timer);
        }
    }, []);

    // Validate if location is within delivery zone
    const validateLocation = (loc) => {
        const withinZone = isWithinDeliveryZone(loc);
        setIsWithinZone(withinZone);
        onValidationChange(withinZone);

        if (!withinZone && !hasShownDeliveryZoneWarning.current) {
            hasShownDeliveryZoneWarning.current = true;
            Alert.alert(
                "Outside Delivery Zone",
                "The selected location appears to be outside our delivery zone. You can still save this address, but we may not be able to deliver to this location.",
                [{ text: "OK" }],
                { cancelable: true }
            );
        }
    };

    // Get current location
    const getCurrentLocation = async () => {
        try {
            setLoading(true);
            setError(null);

            const { status } = await Location.requestForegroundPermissionsAsync();

            if (status !== 'granted') {
                setError('Location permission denied');
                setLoading(false);

                Alert.alert(
                    "Location Permission Required",
                    "Please enable location services to use this feature.",
                    [{ text: "OK" }],
                    { cancelable: true }
                );
                return;
            }

            const position = await Location.getCurrentPositionAsync({
                accuracy: Location.Accuracy.Highest,
                maximumAge: 10000
            });

            const newLocation = {
                latitude: position.coords.latitude,
                longitude: position.coords.longitude
            };

            setLocation(newLocation);
            validateLocation(newLocation);

            if (mapRef.current) {
                mapRef.current.animateToRegion({
                    ...newLocation,
                    latitudeDelta: 0.004, // Approximately 300-400m view
                    longitudeDelta: 0.004 * ASPECT_RATIO
                }, 500);
            }

            // Get address from coordinates
            fetchAddressFromCoordinates(newLocation);

        } catch (err) {
            console.error('Error getting current location:', err);
            setError('Could not get your location');

            Alert.alert(
                "Location Error",
                "We couldn't get your current location. Please make sure location services are enabled and try again, or manually select a location on the map.",
                [{ text: "OK" }],
                { cancelable: true }
            );
        } finally {
            setLoading(false);
        }
    };

    // Fetch address details from coordinates
    const fetchAddressFromCoordinates = async (coords) => {
        try {
            const result = await Location.reverseGeocodeAsync({
                latitude: coords.latitude,
                longitude: coords.longitude
            });

            if (result && result.length > 0) {
                const address = result[0];
                console.log('Address from map:', address);

                // Update address details with fetched information
                // Keep existing values if the API doesn't return specific fields
                // Map city to district and ensure correct field mapping
                setAddressDetails(prev => ({
                    ...prev,
                    street: address.street || prev.street,
                    locality: address.district || address.region || prev.locality,
                    district: address.city || 'Vellore', // Map city to district
                    pincode: address.postalCode || prev.pincode
                }));

                // Don't show alert when address details are retrieved
                console.log("Address details retrieved, user can edit before saving");

                return address;
            }
            return null;
        } catch (error) {
            console.error('Error getting address:', error);
            return null;
        }
    };

    // Handle map region change
    const handleRegionChange = (region) => {
        setLocation({
            latitude: region.latitude,
            longitude: region.longitude
        });
        validateLocation({
            latitude: region.latitude,
            longitude: region.longitude
        });
    };

    // Handle region change complete
    const handleRegionChangeComplete = async (region) => {
        // Only update location coordinates without fetching address details
        setLocation({
            latitude: region.latitude,
            longitude: region.longitude
        });

        // Don't fetch address details automatically when map moves
        // This prevents continuous alerts and unwanted address updates
    };

    // Set location and proceed to address form with confirmation alert
    const handleSetLocation = () => {
        if (location) {
            // Show confirmation alert before proceeding
            Alert.alert(
                "Confirm Delivery Location",
                "Would you like to deliver your order to this location?",
                [
                    {
                        text: "Cancel",
                        style: "cancel"
                    },
                    {
                        text: "Confirm",
                        onPress: () => setShowAddressForm(true)
                    }
                ],
                { cancelable: true }
            );
        } else {
            Alert.alert(
                "No Location Selected",
                "Please select a location first.",
                [{ text: "OK" }]
            );
        }
    };

    // Validate pincode and show appropriate message
    const validatePincode = (pincode) => {
        const message = getPincodeMessage(pincode);
        setPincodeMessage(message);
        setCanSaveAddress(message.type === 'success');

        if (message.type !== 'success') {
            setShowPincodeAlert(true);
        }

        return message.type === 'success';
    };

    // Validate all address fields
    const validateAddressFields = () => {
        let isValid = true;
        const errors = {
            flatNumber: '',
            street: '',
            locality: '',
            district: '',
            pincode: ''
        };

        // Validate Flat/House Number
        if (!addressDetails.flatNumber.trim()) {
            errors.flatNumber = 'Flat/House number is required';
            isValid = false;
        }

        // Validate Street Name
        if (!addressDetails.street.trim()) {
            errors.street = 'Street name is required';
            isValid = false;
        }

        // Validate Area/Locality
        if (!addressDetails.locality.trim()) {
            errors.locality = 'Area/Locality is required';
            isValid = false;
        }

        // Validate District
        if (!addressDetails.district.trim()) {
            errors.district = 'District is required';
            isValid = false;
        }

        // Validate Pincode
        if (!addressDetails.pincode.trim()) {
            errors.pincode = 'Pincode is required';
            isValid = false;
        } else if (!/^\d{6}$/.test(addressDetails.pincode.trim())) {
            errors.pincode = 'Pincode must be 6 digits';
            isValid = false;
        } else {
            // Validate pincode for service availability
            const pincodeValid = validatePincode(addressDetails.pincode.trim());
            if (!pincodeValid) {
                errors.pincode = 'Service not available in this pincode';
                isValid = false;
            }
        }

        setFieldErrors(errors);
        return isValid;
    };

    // Save complete address
    const saveAddress = () => {
        // Check if address can be saved (valid pincode)
        if (!canSaveAddress) {
            const message = getPincodeMessage(addressDetails.pincode.trim());
            setPincodeMessage(message);
            setShowPincodeAlert(true);
            return;
        }

        // Validate all required fields
        if (!validateAddressFields()) {
            Alert.alert(
                "Missing or Invalid Information",
                "Please fill in all required fields correctly.",
                [{ text: "OK" }]
            );
            return;
        }

        // Format the complete address in standardized format
        const formattedAddress = {
            // If editing, preserve the original ID
            ...(existingAddress?._id ? { _id: existingAddress._id } : {}),

            // Preserve other important fields if editing
            ...(existingAddress?.isPrimary ? { isPrimary: existingAddress.isPrimary } : {}),
            ...(existingAddress?.isDefault ? { isDefault: existingAddress.isDefault } : {}),
            ...(existingAddress?.isWithinDeliveryZone ? { isWithinDeliveryZone: existingAddress.isWithinDeliveryZone } : {}),

            coordinates: location,
            // Map UI field names to backend field names
            doorNo: addressDetails.flatNumber.trim(),
            flatNumber: addressDetails.flatNumber.trim(), // Add this for compatibility
            streetName: addressDetails.street.trim(),
            street: addressDetails.street.trim(), // Add this for compatibility
            area: addressDetails.locality.trim(),
            locality: addressDetails.locality.trim(), // Add this for compatibility
            district: addressDetails.district.trim(),
            city: addressDetails.district.trim(), // Add this for compatibility
            pincode: addressDetails.pincode.trim(),
            addressType: addressDetails.addressType,
            type: addressDetails.addressType, // Add this for compatibility
            // Create standardized full address string
            fullAddress: `${addressDetails.flatNumber.trim()}, ${addressDetails.street.trim()}, ${addressDetails.locality.trim()}, ${addressDetails.district.trim()}, ${addressDetails.pincode.trim()}`
        };

        // If editing an existing address, include the ID
        if (existingAddress && existingAddress._id) {
            formattedAddress._id = existingAddress._id;
        }

        // Pass the complete address back to parent component
        onLocationSelected(formattedAddress);
        onClose();
    };

    // Render address form
    const renderAddressForm = () => (
        <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={styles.addressFormContainer}
        >
            <ScrollView showsVerticalScrollIndicator={false}>
                <View style={styles.addressFormHeader}>
                    <Text style={styles.addressFormTitle}>
                        {existingAddress ? "Edit Primary Address" : "Complete Your Address"}
                    </Text>
                    <TouchableOpacity
                        style={styles.backButton}
                        onPress={existingAddress ? onClose : () => setShowAddressForm(false)}
                    >
                        <MaterialIcons name="arrow-back" size={24} color="#FFFFFF" />
                    </TouchableOpacity>
                </View>

                {/* Information banner */}
                <View style={styles.infoBanner}>
                    <MaterialIcons name="info" size={18} color="#3B82F6" />
                    <Text style={styles.infoText}>
                        Please enter your complete address details. Fields marked with * are required.
                    </Text>
                </View>

                <View style={styles.formField}>
                    <Text style={styles.fieldLabel}>Flat/House/Office Number <Text style={styles.requiredStar}>*</Text></Text>
                    <TextInput
                        style={[styles.input, fieldErrors.flatNumber ? styles.inputError : null]}
                        value={addressDetails.flatNumber}
                        onChangeText={(text) => {
                            setAddressDetails({...addressDetails, flatNumber: text});
                            if (text.trim()) setFieldErrors({...fieldErrors, flatNumber: ''});
                        }}
                        placeholder="e.g., Flat 101, House No. 42"
                    />
                    {fieldErrors.flatNumber ? (
                        <Text style={styles.errorText}>{fieldErrors.flatNumber}</Text>
                    ) : null}
                </View>

                <View style={styles.formField}>
                    <Text style={styles.fieldLabel}>Street Name <Text style={styles.requiredStar}>*</Text></Text>
                    <TextInput
                        style={[styles.input, fieldErrors.street ? styles.inputError : null]}
                        value={addressDetails.street}
                        onChangeText={(text) => {
                            setAddressDetails({...addressDetails, street: text});
                            if (text.trim()) setFieldErrors({...fieldErrors, street: ''});
                        }}
                        placeholder="e.g., Main Street"
                    />
                    {fieldErrors.street ? (
                        <Text style={styles.errorText}>{fieldErrors.street}</Text>
                    ) : null}
                </View>

                <View style={styles.formField}>
                    <Text style={styles.fieldLabel}>Area/Locality <Text style={styles.requiredStar}>*</Text></Text>
                    <TextInput
                        style={[styles.input, fieldErrors.locality ? styles.inputError : null]}
                        value={addressDetails.locality}
                        onChangeText={(text) => {
                            setAddressDetails({...addressDetails, locality: text});
                            if (text.trim()) setFieldErrors({...fieldErrors, locality: ''});
                        }}
                        placeholder="e.g., Gandhi Nagar"
                    />
                    {fieldErrors.locality ? (
                        <Text style={styles.errorText}>{fieldErrors.locality}</Text>
                    ) : null}
                </View>

                <View style={styles.formRow}>
                    <View style={[styles.formField, {flex: 1, marginRight: 10}]}>
                        <Text style={styles.fieldLabel}>District <Text style={styles.requiredStar}>*</Text></Text>
                        <TextInput
                            style={[styles.input, fieldErrors.district ? styles.inputError : null]}
                            value={addressDetails.district}
                            onChangeText={(text) => {
                                setAddressDetails({...addressDetails, district: text});
                                if (text.trim()) setFieldErrors({...fieldErrors, district: ''});
                            }}
                            placeholder="e.g., Vellore"
                        />
                        {fieldErrors.district ? (
                            <Text style={styles.errorText}>{fieldErrors.district}</Text>
                        ) : null}
                    </View>
                    <View style={[styles.formField, {flex: 1}]}>
                        <Text style={styles.fieldLabel}>Pincode <Text style={styles.requiredStar}>*</Text></Text>
                        <TextInput
                            style={[styles.input, fieldErrors.pincode ? styles.inputError : null]}
                            value={addressDetails.pincode}
                            onChangeText={(text) => {
                                setAddressDetails({...addressDetails, pincode: text});
                                if (/^\d{6}$/.test(text.trim())) {
                                    setFieldErrors({...fieldErrors, pincode: ''});
                                    // Validate pincode when user enters 6 digits
                                    validatePincode(text.trim());
                                }
                            }}
                            placeholder="e.g., 632001"
                            keyboardType="numeric"
                            maxLength={6}
                        />
                        {fieldErrors.pincode ? (
                            <Text style={styles.errorText}>{fieldErrors.pincode}</Text>
                        ) : null}
                    </View>
                </View>

                <View style={[styles.formField, styles.addressTypeField]}>
                    <Text style={[styles.fieldLabel, styles.addressTypeLabel]}>Select Address Type</Text>
                    <View style={styles.addressTypeContainer}>
                        {[
                            { type: 'Home', icon: 'home' },
                            { type: 'Work', icon: 'work' },
                            { type: 'Other', icon: 'place' }
                        ].map(item => (
                            <TouchableOpacity
                                key={item.type}
                                style={[
                                    styles.addressTypeButton,
                                    addressDetails.addressType === item.type && styles.addressTypeButtonSelected
                                ]}
                                onPress={() => {
                                    // Add a small vibration effect when selecting an address type
                                    if (Platform.OS === 'android' && Vibration) {
                                        Vibration.vibrate(50);
                                    }
                                    setAddressDetails({...addressDetails, addressType: item.type});
                                }}
                            >
                                <View style={styles.addressTypeContent}>
                                    <MaterialIcons
                                        name={item.icon}
                                        size={18}
                                        color={addressDetails.addressType === item.type ? '#FFFFFF' : '#555'}
                                        style={styles.addressTypeIcon}
                                    />
                                    <Text style={[
                                        styles.addressTypeText,
                                        addressDetails.addressType === item.type && styles.addressTypeTextSelected
                                    ]}>{item.type}</Text>
                                </View>
                            </TouchableOpacity>
                        ))}
                    </View>
                </View>

                <TouchableOpacity style={styles.saveButton} onPress={saveAddress}>
                    <Text style={styles.saveButtonText}>
                        {existingAddress ? "Update Primary Address" : "Save Address"}
                    </Text>
                </TouchableOpacity>

                {!existingAddress && (
                    <TouchableOpacity
                        style={styles.cancelButton}
                        onPress={() => setShowAddressForm(false)}
                    >
                        <Text style={styles.cancelButtonText}>Back to Map</Text>
                    </TouchableOpacity>
                )}
                {existingAddress && (
                    <TouchableOpacity
                        style={styles.cancelButton}
                        onPress={onClose}
                    >
                        <Text style={styles.cancelButtonText}>Cancel</Text>
                    </TouchableOpacity>
                )}
            </ScrollView>

            {/* Pincode Alert Modal */}
            <PincodeAlert
                visible={showPincodeAlert}
                onClose={() => setShowPincodeAlert(false)}
                onBrowseProducts={() => {
                    setShowPincodeAlert(false);
                    // Navigate to home screen to browse products
                    onClose();
                }}
                pincodeMessage={pincodeMessage}
                showBrowseOption={true}
            />
        </KeyboardAvoidingView>
    );

    // If showing address form, render it
    if (showAddressForm) {
        return renderAddressForm();
    }

    // If editing an existing address, don't show the map picker at all
    if (existingAddress) {
        // Force show the address form for editing
        if (!showAddressForm) {
            setShowAddressForm(true);
        }
        return renderAddressForm();
    }

    // Render map picker for new addresses only
    return (
        <KeyboardAvoidingView
            style={styles.container}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
            <View style={styles.mapContainer}>
                <MapView
                    ref={mapRef}
                    style={styles.map}
                    provider={PROVIDER_GOOGLE}
                    initialRegion={{
                        ...location,
                        latitudeDelta: LATITUDE_DELTA,
                        longitudeDelta: LONGITUDE_DELTA
                    }}
                    showsUserLocation={true}
                    showsMyLocationButton={false}
                    showsBuildings={true}
                    showsTraffic={true}
                    showsIndoors={true}
                    showsPointsOfInterest={true}
                    showsCompass={true}
                    mapType="standard"
                    onRegionChange={handleRegionChange}
                    onRegionChangeComplete={handleRegionChangeComplete}
                />

                {/* Fixed marker label in center */}
                <View style={styles.fixedMarkerContainer}>
                    <Animated.View
                        style={[
                            styles.markerPulse,
                            {
                                transform: [{ scale: pulseAnim }],
                                opacity: pulseOpacity
                            }
                        ]}
                    />
                    <View style={styles.markerLabel}>
                        <Text style={styles.markerLabelText}>The order is delivered here</Text>
                        <View style={styles.markerPointer} />
                    </View>
                </View>

                {loading && (
                    <View style={styles.loadingOverlay}>
                        <ActivityIndicator size="large" color="#A31621" />
                    </View>
                )}

                {error && (
                    <View style={styles.errorContainer}>
                        <Text style={styles.errorText}>{error}</Text>
                    </View>
                )}

                <TouchableOpacity
                    style={styles.currentLocationButton}
                    onPress={getCurrentLocation}
                >
                    <MaterialIcons name="my-location" size={24} color="#A31621" />
                </TouchableOpacity>

                {/* Set Location Button */}
                <TouchableOpacity
                    style={styles.setLocationButton}
                    onPress={handleSetLocation}
                >
                    <MaterialIcons name="check-circle" size={20} color="white" />
                    <Text style={styles.setLocationButtonText}>Set Location</Text>
                </TouchableOpacity>

                {!isWithinZone && (
                    <View style={styles.warningBanner}>
                        <MaterialIcons name="warning" size={20} color="#fff" />
                        <View style={styles.warningTextContainer}>
                            <Text style={styles.warningText}>Outside delivery zone</Text>
                            <Text style={styles.warningSubtext}>You can still select this location</Text>
                        </View>
                    </View>
                )}

                <View style={styles.mapAttributionContainer} />
            </View>

            {/* Pincode Alert Modal */}
            <PincodeAlert
                visible={showPincodeAlert}
                onClose={() => setShowPincodeAlert(false)}
                onBrowseProducts={() => {
                    setShowPincodeAlert(false);
                    // Navigate to home screen to browse products
                    onClose();
                }}
                pincodeMessage={pincodeMessage}
                showBrowseOption={true}
            />
        </KeyboardAvoidingView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff'
    },

    mapContainer: {
        flex: 1,
        position: 'relative'
    },
    map: {
        ...StyleSheet.absoluteFillObject
    },
    fixedMarkerContainer: {
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: [{ translateX: -100 }, { translateY: -50 }], // Center the container
        alignItems: 'center',
        justifyContent: 'center',
        width: 200,
        height: 50,
    },
    markerPulse: {
        position: 'absolute',
        width: 10,
        height: 10,
        borderRadius: 5,
        backgroundColor: 'rgba(163, 22, 33, 0.9)',
        borderWidth: 1.5,
        borderColor: '#A31621',
        bottom: -12, // Position at the tip of the pointer
        left: 92, // Aligned with the pointer
        zIndex: -1
    },
    markerLabel: {
        backgroundColor: 'rgba(0,0,0,0.85)',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 6,
        borderWidth: 1.5,
        borderColor: '#A31621',
        width: 'auto',
        maxWidth: 200,
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3,
        elevation: 4,
        marginBottom: 8, // Space for the pointer
        marginLeft: -5 // Adjust to center with the pointer
    },
    markerPointer: {
        position: 'absolute',
        bottom: -5,
        left: 90, // Adjusted to align with the label
        width: 10,
        height: 10,
        backgroundColor: 'rgba(0,0,0,0.85)',
        borderLeftWidth: 1.5,
        borderBottomWidth: 1.5,
        borderColor: '#A31621',
        transform: [{ rotate: '45deg' }],
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.15,
        shadowRadius: 1,
        elevation: 2
    },
    markerLabelText: {
        color: 'white',
        fontSize: 13.5,
        fontWeight: '600',
        textAlign: 'center',
        letterSpacing: 0.2,
        flexShrink: 1,
        flexWrap: 'nowrap'
    },
    loadingOverlay: {
        ...StyleSheet.absoluteFillObject,
        backgroundColor: 'rgba(255,255,255,0.7)',
        alignItems: 'center',
        justifyContent: 'center'
    },
    errorContainer: {
        position: 'absolute',
        top: '50%',
        left: 20,
        right: 20,
        backgroundColor: 'rgba(255,0,0,0.7)',
        padding: 10,
        borderRadius: 8,
        alignItems: 'center'
    },
    errorText: {
        color: 'white',
        textAlign: 'center'
    },
    currentLocationButton: {
        position: 'absolute',
        bottom: 80,
        right: 20,
        backgroundColor: 'white',
        width: 50,
        height: 50,
        borderRadius: 25,
        alignItems: 'center',
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.3,
        shadowRadius: 4,
        elevation: 5
    },
    setLocationButton: {
        position: 'absolute',
        bottom: 20,
        left: 20,
        right: 20,
        backgroundColor: '#A31621',
        height: 50,
        borderRadius: 8,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.3,
        shadowRadius: 4,
        elevation: 5
    },
    setLocationButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 16,
        marginLeft: 10
    },
    warningBanner: {
        position: 'absolute',
        top: 10,
        left: 10,
        right: 10,
        backgroundColor: '#ff6b6b',
        padding: 8,
        borderRadius: 8,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.3,
        shadowRadius: 2,
        elevation: 3
    },
    warningTextContainer: {
        flex: 1,
        marginLeft: 5
    },
    warningText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14
    },
    warningSubtext: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 12
    },
    mapAttributionContainer: {
        position: 'absolute',
        bottom: 0,
        right: 0,
        backgroundColor: 'transparent',
        width: 100,
        height: 20
    },
    addressFormContainer: {
        flex: 1,
        backgroundColor: '#f8f8f8',
        padding: 0
    },
    addressFormHeader: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#A31621',
        paddingVertical: 15,
        paddingHorizontal: 20,
        marginBottom: 20,
        borderBottomLeftRadius: 20,
        borderBottomRightRadius: 20,
        elevation: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        position: 'relative'
    },
    addressFormTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#FFFFFF',
        textAlign: 'center'
    },
    backButton: {
        position: 'absolute',
        left: 15,
        padding: 5,
        borderRadius: 20
    },
    // New styles for the info banner
    infoBanner: {
        backgroundColor: '#f8f8f8',
        borderRadius: 8,
        padding: 12,
        marginHorizontal: 20,
        marginBottom: 15,
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#e0e0e0'
    },
    infoText: {
        color: '#555',
        fontSize: 13,
        marginLeft: 8,
        flex: 1
    },
    formField: {
        marginBottom: 18,
        marginHorizontal: 20
    },
    formRow: {
        flexDirection: 'row',
        marginBottom: 18,
        marginHorizontal: 20
    },
    fieldLabel: {
        fontSize: 15,
        fontWeight: '600',
        marginBottom: 8,
        color: '#333',
        letterSpacing: 0.3
    },
    input: {
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 10,
        padding: 12,
        fontSize: 16,
        backgroundColor: '#FFFFFF',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 1,
        elevation: 1
    },
    addressTypeContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginHorizontal: 20,
        marginBottom: 20
    },
    addressTypeButton: {
        flex: 1,
        padding: 10,
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 10,
        marginHorizontal: 5,
        alignItems: 'center',
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 1,
        height: 60,
        backgroundColor: '#FFFFFF'
    },
    addressTypeButtonSelected: {
        backgroundColor: '#A31621',
        borderColor: '#A31621',
        transform: [{scale: 1.03}]
    },
    addressTypeContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center'
    },
    addressTypeIcon: {
        marginRight: 5
    },
    addressTypeText: {
        color: '#333',
        fontWeight: '600',
        fontSize: 14
    },
    addressTypeTextSelected: {
        color: 'white'
    },
    addressTypeField: {
        marginTop: 10,
        marginBottom: 15
    },
    addressTypeLabel: {
        fontSize: 16,
        fontWeight: '700',
        color: '#222',
        marginBottom: 12,
        textAlign: 'center'
    },
    saveButton: {
        backgroundColor: '#A31621',
        padding: 15,
        borderRadius: 10,
        alignItems: 'center',
        marginTop: 25,
        marginHorizontal: 20,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 3,
        elevation: 3
    },
    saveButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 17,
        letterSpacing: 0.5
    },
    // New styles for the cancel button
    cancelButton: {
        backgroundColor: '#f5f5f5',
        padding: 15,
        borderRadius: 10,
        alignItems: 'center',
        marginTop: 12,
        marginBottom: 30,
        marginHorizontal: 20,
        borderWidth: 1,
        borderColor: '#ddd',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 1
    },
    cancelButtonText: {
        color: '#555',
        fontWeight: 'bold',
        fontSize: 16
    },
    // New styles for validation
    requiredStar: {
        color: '#A31621',
        fontWeight: 'bold',
        fontSize: 16
    },
    inputError: {
        borderColor: '#A31621',
        backgroundColor: '#FFF5F5',
        borderWidth: 1.5
    },
    errorText: {
        color: '#A31621',
        fontSize: 13,
        marginTop: 6,
        marginLeft: 2,
        fontWeight: '500'
    }
});

export default DeliveryLocationPicker;
