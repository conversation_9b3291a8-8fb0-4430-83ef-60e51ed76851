import React from 'react';
import { View, Text } from 'react-native';
import { MaterialIcons, MaterialCommunityIcons } from '@expo/vector-icons';

// Default nutrition values for different product categories
const DEFAULT_NUTRITION = {
  // Meat products (per 100g)
  meat: {
    energy: 127,
    protein: 21.6,
    carbs: 0,
    fat: 4.5,
    benefits: ["High in Protein", "Rich in Vitamins", "Iron Source"]
  },
  // Chicken products (per 100g)
  chicken: {
    energy: 165,
    protein: 31,
    carbs: 0,
    fat: 3.6,
    benefits: ["Lean Protein", "Low Fat", "B Vitamins"]
  },
  // Fish products (per 100g)
  fish: {
    energy: 206,
    protein: 22,
    carbs: 0,
    fat: 12,
    benefits: ["Omega-3 Fatty Acids", "High Protein", "Heart Healthy"]
  },
  // Mutton products (per 100g)
  mutton: {
    energy: 294,
    protein: 25,
    carbs: 0,
    fat: 21,
    benefits: ["High Protein", "Iron Rich", "Zinc Source"]
  },
  // Default fallback
  default: {
    energy: 150,
    protein: 20,
    carbs: 2,
    fat: 6,
    benefits: ["Nutritious", "Fresh Quality"]
  }
};

const NutritionFacts = ({ product, selectedWeight }) => {
  // Get nutrition data based on product category/name
  const getNutritionData = () => {
    const productName = product.name?.toLowerCase() || '';
    const categoryName = product.category?.name?.toLowerCase() || '';

    if (productName.includes('chicken') || categoryName.includes('chicken')) {
      return DEFAULT_NUTRITION.chicken;
    } else if (productName.includes('fish') || categoryName.includes('fish')) {
      return DEFAULT_NUTRITION.fish;
    } else if (productName.includes('mutton') || productName.includes('goat') || categoryName.includes('mutton')) {
      return DEFAULT_NUTRITION.mutton;
    } else if (productName.includes('meat') || categoryName.includes('meat')) {
      return DEFAULT_NUTRITION.meat;
    }

    return DEFAULT_NUTRITION.default;
  };

  // Calculate nutrition based on selected weight
  const calculateNutritionForWeight = (nutrition) => {
    if (!selectedWeight) return nutrition;

    // Extract weight in grams
    const weightInGrams = selectedWeight.includes('kg')
      ? parseInt(selectedWeight) * 1000
      : parseInt(selectedWeight);

    // Calculate multiplier (nutrition is per 100g)
    const multiplier = weightInGrams / 100;

    return {
      energy: Math.round((nutrition.energy || 0) * multiplier),
      protein: Math.round((nutrition.protein || 0) * multiplier * 10) / 10,
      carbs: Math.round((nutrition.carbs || 0) * multiplier * 10) / 10,
      fat: Math.round((nutrition.fat || 0) * multiplier * 10) / 10,
      benefits: nutrition.benefits || []
    };
  };

  const nutritionData = getNutritionData();
  const adjustedNutrition = calculateNutritionForWeight(nutritionData);

  // Don't render if no nutrition data available
  if (!nutritionData.energy && !nutritionData.protein) {
    return null;
  }

  return (
    <View className="p-4 bg-white rounded-xl shadow-sm mb-3">
      <Text className="text-lg font-semibold text-gray-800 mb-3">Nutrition Facts</Text>

      {/* Nutrition Values Grid */}
      <View className="flex-row justify-between bg-gray-100 p-4 rounded-xl mb-4">
        <View className="items-center">
          <Text className="text-gray-500 text-xs">Energy</Text>
          <Text className="text-lg font-bold text-gray-800">
            {adjustedNutrition.energy || '-'}
          </Text>
          <Text className="text-gray-500 text-xs">Kcal</Text>
        </View>
        <View className="w-px h-full bg-gray-300" />
        <View className="items-center">
          <Text className="text-gray-500 text-xs">Protein</Text>
          <Text className="text-lg font-bold text-gray-800">
            {adjustedNutrition.protein || '-'}
          </Text>
          <Text className="text-gray-500 text-xs">g</Text>
        </View>
        <View className="w-px h-full bg-gray-300" />
        <View className="items-center">
          <Text className="text-gray-500 text-xs">Carbs</Text>
          <Text className="text-lg font-bold text-gray-800">
            {adjustedNutrition.carbs || '-'}
          </Text>
          <Text className="text-gray-500 text-xs">g</Text>
        </View>
        <View className="w-px h-full bg-gray-300" />
        <View className="items-center">
          <Text className="text-gray-500 text-xs">Fat</Text>
          <Text className="text-lg font-bold text-gray-800">
            {adjustedNutrition.fat || '-'}
          </Text>
          <Text className="text-gray-500 text-xs">g</Text>
        </View>
      </View>

      {/* Benefits Section */}
      <View>
        <View className="flex-row items-center mb-3">
          <View className="w-10 h-10 bg-white shadow-sm rounded-full items-center justify-center">
            <MaterialIcons name="info-outline" size={20} color="#10B981" />
          </View>
          <View className="ml-3">
            <Text className="text-gray-800 font-medium">Nutritional Info</Text>
            <Text className="text-gray-600 text-xs mt-1">
              Values shown per {selectedWeight || '100g'} serving
            </Text>
          </View>
        </View>

        {/* Dynamic Benefits */}
        {adjustedNutrition.benefits && adjustedNutrition.benefits.length > 0 && (
          <>
            <View className="h-px bg-gray-100 mb-3" />
            {adjustedNutrition.benefits.slice(0, 2).map((benefit, index) => (
              <View key={index} className="flex-row items-center mb-3">
                <View className="w-10 h-10 bg-white shadow-sm rounded-full items-center justify-center">
                  <MaterialCommunityIcons
                    name={index === 0 ? "heart-pulse" : "bone"}
                    size={20}
                    color={index === 0 ? "#EF4444" : "#3B82F6"}
                  />
                </View>
                <View className="ml-3">
                  <Text className="text-gray-800 font-medium">{benefit}</Text>
                  <Text className="text-gray-600 text-xs mt-1">
                    {index === 0 ? "Essential for muscle growth" : "Contains essential nutrients"}
                  </Text>
                </View>
              </View>
            ))}
          </>
        )}
      </View>
    </View>
  );
};

export default NutritionFacts;
