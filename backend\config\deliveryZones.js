/**
 * Delivery Zone Configuration
 *
 * This file defines the delivery zones for the application.
 * It includes valid pincodes and geographical boundaries.
 */

// List of valid pincodes for delivery
const validPincodes = [
    // Chennai pincodes
    '600001', '600002', '600003', '600004', '600005',
    '600006', '600007', '600008', '600009', '600010',
    '600011', '600012', '600013', '600014', '600015',
    '600016', '600017', '600018', '600019', '600020',
    '600021', '600022', '600023', '600024', '600025',

    // Vellore pincodes
    '632001', '632002', '632003', '632004', '632005', '632006', '632007', '632008', '632009',

    // Add more valid pincodes as needed
];

// Central points of delivery operations
const centralPoints = [
    {
        name: 'Chennai',
        latitude: 13.0827,
        longitude: 80.2707
    },
    {
        name: 'Vellore',
        latitude: 12.9165,
        longitude: 79.1325
    }
];

// Use Chennai as the default central point for backward compatibility
const centralPoint = centralPoints[0];

// Maximum delivery radius in kilometers
const maxDeliveryRadius = 15;

// Polygon-based delivery zones (for more precise boundary definition)
const deliveryZonePolygons = [
    // Chennai city center area
    [
        { latitude: 13.0569, longitude: 80.2425 }, // Southwest corner
        { latitude: 13.0569, longitude: 80.2989 }, // Southeast corner
        { latitude: 13.1085, longitude: 80.2989 }, // Northeast corner
        { latitude: 13.1085, longitude: 80.2425 }  // Northwest corner
    ],
    // Vellore city area
    [
        { latitude: 12.8970, longitude: 79.1160 }, // Southwest corner
        { latitude: 12.8970, longitude: 79.1560 }, // Southeast corner
        { latitude: 12.9370, longitude: 79.1560 }, // Northeast corner
        { latitude: 12.9370, longitude: 79.1160 }  // Northwest corner
    ]
];

// Use Chennai polygon as the default for backward compatibility
const deliveryZonePolygon = deliveryZonePolygons[0];

// Time slots configuration
const deliveryTimeSlots = {
    morning: {
        start: 9, // 9 AM
        end: 12   // 12 PM
    },
    evening: {
        start: 17, // 5 PM
        end: 19    // 7 PM
    }
};

module.exports = {
    validPincodes,
    centralPoint,
    centralPoints,
    maxDeliveryRadius,
    deliveryZonePolygon,
    deliveryZonePolygons,
    deliveryTimeSlots
};
