const User = require('../models/User');
const Order = require('../models/Order');

/**
 * Get user profile
 * @route GET /api/users/profile
 * @access Private
 */
const getUserProfile = async (req, res) => {
    try {
        const user = await User.findById(req.user._id)
            .select('-otpCode -otpExpiry -refreshToken');

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        res.status(200).json(user);
    } catch (error) {
        console.error('Error fetching user profile:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Update user profile
 * @route PUT /api/users/profile
 * @access Private
 */
const updateUserProfile = async (req, res) => {
    try {
        const { name, email, address } = req.body;
        const mongoose = require('mongoose');

        // Find user
        const user = await User.findById(req.user._id);
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Update basic fields
        if (name) user.name = name;
        if (email) user.email = email;

        // Handle address update
        if (address) {
            console.log('Updating address in user profile:', address);

            // Update the main address field
            user.address = {
                doorNo: address.doorNo || '',
                streetName: address.streetName || '',
                area: address.area || '',
                district: address.district || '',
                pincode: address.pincode || '',
                fullAddress: address.fullAddress ||
                    `${address.doorNo || ''}, ${address.streetName || ''}, ${address.area || ''}, ${address.district || ''}, ${address.pincode || ''}`.trim(),
                coordinates: {
                    latitude: address.coordinates?.latitude || address.latitude || null,
                    longitude: address.coordinates?.longitude || address.longitude || null
                },
                latitude: address.coordinates?.latitude || address.latitude || null,
                longitude: address.coordinates?.longitude || address.longitude || null,
                addressType: address.addressType || 'Home'
            };

            // Initialize addresses array if it doesn't exist
            if (!user.addresses) {
                user.addresses = [];
            }

            // Check if this address should be primary
            const isPrimary = address.isPrimary !== undefined ? address.isPrimary : true;

            // If this is a primary address, update all other addresses to not be primary
            if (isPrimary) {
                user.addresses.forEach(addr => {
                    addr.isPrimary = false;
                });
            }

            // Extract coordinates from the address
            const lat = address.coordinates?.latitude || address.latitude || null;
            const lng = address.coordinates?.longitude || address.longitude || null;

            // Check if we need to add this to the addresses array
            // First, look for an existing address with the same details
            const existingAddressIndex = user.addresses.findIndex(addr =>
                addr.doorNo === address.doorNo &&
                addr.streetName === address.streetName &&
                addr.area === address.area &&
                addr.district === address.district &&
                addr.pincode === address.pincode
            );

            if (existingAddressIndex >= 0) {
                // Update the existing address
                console.log('Updating existing address in addresses array at index:', existingAddressIndex);

                user.addresses[existingAddressIndex] = {
                    ...user.addresses[existingAddressIndex],
                    doorNo: address.doorNo || '',
                    streetName: address.streetName || '',
                    area: address.area || '',
                    district: address.district || '',
                    pincode: address.pincode || '',
                    fullAddress: address.fullAddress ||
                        `${address.doorNo || ''}, ${address.streetName || ''}, ${address.area || ''}, ${address.district || ''}, ${address.pincode || ''}`.trim(),
                    coordinates: {
                        latitude: lat,
                        longitude: lng
                    },
                    latitude: lat,
                    longitude: lng,
                    isPrimary: isPrimary,
                    addressType: address.addressType || 'Home'
                };
            } else {
                // Add as a new address
                console.log('Adding new address to addresses array');

                const newAddress = {
                    _id: new mongoose.Types.ObjectId(),
                    type: address.addressType || 'Home',
                    doorNo: address.doorNo || '',
                    streetName: address.streetName || '',
                    area: address.area || '',
                    district: address.district || '',
                    pincode: address.pincode || '',
                    fullAddress: address.fullAddress ||
                        `${address.doorNo || ''}, ${address.streetName || ''}, ${address.area || ''}, ${address.district || ''}, ${address.pincode || ''}`.trim(),
                    coordinates: {
                        latitude: lat,
                        longitude: lng
                    },
                    latitude: lat,
                    longitude: lng,
                    isDefault: false,
                    isPrimary: isPrimary,
                    createdAt: new Date(),
                    isWithinDeliveryZone: address.isWithinDeliveryZone !== undefined ? address.isWithinDeliveryZone : true,
                    addressType: address.addressType || 'Home'
                };

                user.addresses.push(newAddress);
            }
        }

        const updatedUser = await user.save();
        console.log('User profile updated successfully');

        res.status(200).json({
            _id: updatedUser._id,
            name: updatedUser.name,
            email: updatedUser.email,
            number: updatedUser.number,
            address: updatedUser.address,
            addresses: updatedUser.addresses
        });
    } catch (error) {
        console.error('Error updating user profile:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Get user's coin history
 * @route GET /api/users/coins
 * @access Private
 */
const getUserCoins = async (req, res) => {
    try {
        const user = await User.findById(req.user._id)
            .select('coins usedCoins coinsHistory usedCoupons')
            .populate({
                path: 'coinsHistory.orderId',
                select: 'orderNumber totalAmount status'
            });

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Calculate active coins (not expired)
        const now = new Date();
        const activeCoins = user.coinsHistory
            .filter(coin => coin.type === 'EARNED' && (!coin.expiry || new Date(coin.expiry) > now))
            .reduce((sum, coin) => sum + coin.amount, 0);

        // Calculate used coins
        const usedCoins = user.coinsHistory
            .filter(coin => coin.type === 'USED')
            .reduce((sum, coin) => sum + Math.abs(coin.amount), 0);

        // Calculate expired coins
        const expiredCoins = user.coinsHistory
            .filter(coin => coin.type === 'EARNED' && coin.expiry && new Date(coin.expiry) <= now)
            .reduce((sum, coin) => sum + coin.amount, 0);

        // Calculate refunded coins
        const refundedCoins = user.coinsHistory
            .filter(coin => coin.type === 'REFUNDED')
            .reduce((sum, coin) => sum + Math.abs(coin.amount), 0);

        res.status(200).json({
            totalCoins: user.coins,
            activeCoins,
            usedCoins,
            expiredCoins,
            refundedCoins,
            coinsHistory: user.coinsHistory,
            usedCoupons: user.usedCoupons || []
        });
    } catch (error) {
        console.error('Error fetching user coins:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Get user's order history
 * @route GET /api/users/orders
 * @access Private
 */
const getUserOrders = async (req, res) => {
    try {
        const orders = await Order.find({ userId: req.user._id })
            .sort({ createdAt: -1 })
            .populate('deliveryPartner', 'name phoneNumber');

        res.status(200).json(orders);
    } catch (error) {
        console.error('Error fetching user orders:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

module.exports = {
    getUserProfile,
    updateUserProfile,
    getUserCoins,
    getUserOrders
};