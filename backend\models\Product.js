const mongoose = require("mongoose");

const productSchema = new mongoose.Schema(
  {
    id: { type: String, required: true, unique: true },
    name: { type: String, required: true },
    description: { type: String },
    price: { type: Number, required: true },
    discount_price: { type: Number },
    discountPercentage: { type: Number, default: 0 },
    weight: { type: String },
    pieces: { type: String },
    available: { type: Boolean, default: true },
    isAvailable: { type: Boolean, default: true },
    stock: { type: Number, default: 0 },
    offer: { type: String },
    reward_points: { type: Number, default: 0 },
    category: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Category", // Linking to Category schema
    },
    image: { type: String, required: true }, // Image URL for product
    sold: { type: Number, default: 0 },
    // Nutrition information (per 100g)
    nutrition: {
      energy: { type: Number, default: null }, // in Kcal
      protein: { type: Number, default: null }, // in grams
      carbs: { type: Number, default: null }, // in grams
      fat: { type: Number, default: null }, // in grams
      fiber: { type: Number, default: null }, // in grams
      sodium: { type: Number, default: null }, // in mg
      // Additional nutrition info
      benefits: [{ type: String }], // e.g., ["High in Protein", "Rich in Vitamins"]
      allergens: [{ type: String }], // e.g., ["Contains Gluten", "May contain nuts"]
    }
  },
  { timestamps: true }
);

const Product = mongoose.model("Product", productSchema);
module.exports = Product;
