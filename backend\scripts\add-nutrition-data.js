const mongoose = require('mongoose');
const Product = require('../models/Product');
require('dotenv').config();

// Default nutrition values for different product categories
const NUTRITION_DATA = {
  // Meat products (per 100g)
  meat: {
    energy: 127,
    protein: 21.6,
    carbs: 0,
    fat: 4.5,
    benefits: ["High in Protein", "Rich in Vitamins", "Iron Source"]
  },
  // Chicken products (per 100g)
  chicken: {
    energy: 165,
    protein: 31,
    carbs: 0,
    fat: 3.6,
    benefits: ["Lean Protein", "Low Fat", "B Vitamins"]
  },
  // Fish products (per 100g)
  fish: {
    energy: 206,
    protein: 22,
    carbs: 0,
    fat: 12,
    benefits: ["Omega-3 Fatty Acids", "High Protein", "Heart Healthy"]
  },
  // Mutton products (per 100g)
  mutton: {
    energy: 294,
    protein: 25,
    carbs: 0,
    fat: 21,
    benefits: ["High Protein", "Iron Rich", "Zinc Source"]
  },
  // Default fallback
  default: {
    energy: 150,
    protein: 20,
    carbs: 2,
    fat: 6,
    benefits: ["Nutritious", "Fresh Quality"]
  }
};

// Function to determine nutrition category based on product name
const getNutritionCategory = (productName, categoryName = '') => {
  const name = productName.toLowerCase();
  const category = categoryName.toLowerCase();
  
  if (name.includes('chicken') || category.includes('chicken')) {
    return 'chicken';
  } else if (name.includes('fish') || category.includes('fish')) {
    return 'fish';
  } else if (name.includes('mutton') || name.includes('goat') || category.includes('mutton')) {
    return 'mutton';
  } else if (name.includes('meat') || category.includes('meat')) {
    return 'meat';
  }
  
  return 'default';
};

// Migration function
const addNutritionData = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/meatshop');
    console.log('Connected to MongoDB');

    // Get all products that don't have nutrition data
    const products = await Product.find({
      $or: [
        { nutrition: { $exists: false } },
        { 'nutrition.energy': { $exists: false } }
      ]
    }).populate('category');

    console.log(`Found ${products.length} products without nutrition data`);

    let updatedCount = 0;

    for (const product of products) {
      try {
        // Determine nutrition category
        const categoryName = product.category?.name || '';
        const nutritionCategory = getNutritionCategory(product.name, categoryName);
        const nutritionData = NUTRITION_DATA[nutritionCategory];

        // Update product with nutrition data
        await Product.findByIdAndUpdate(product._id, {
          nutrition: nutritionData
        });

        console.log(`Updated nutrition for: ${product.name} (${nutritionCategory})`);
        updatedCount++;
      } catch (error) {
        console.error(`Error updating product ${product.name}:`, error.message);
      }
    }

    console.log(`\nMigration completed!`);
    console.log(`Updated ${updatedCount} products with nutrition data`);

  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
};

// Run migration if this file is executed directly
if (require.main === module) {
  addNutritionData();
}

module.exports = { addNutritionData, NUTRITION_DATA, getNutritionCategory };
