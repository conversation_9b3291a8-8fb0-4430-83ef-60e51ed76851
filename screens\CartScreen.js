import { View, Text, ScrollView, TouchableOpacity, Image, Alert, Animated, StatusBar } from 'react-native';
import React, { useState, useRef, useEffect } from 'react';
import { Ionicons } from "@expo/vector-icons";
import { useCart } from '../context/CartContext';
import { useUser } from '../context/UserContext';

const CartScreen = ({ navigation }) => {
    const { cartItems, removeFromCart, clearCart, updateCartItemQuantity } = useCart();
    // Removed useCoinsForDiscount and restoreCoins as backend handles this now
    const { getUserCoinsData, currentUser } = useUser();


    const [couponDiscount, setCouponDiscount] = useState(0); // Discount from coupon
    const [appliedCoupon, setAppliedCoupon] = useState(null);
    // const [coinsDiscount, setCoinsDiscount] = useState(0); // Replaced by potentialCoinsDiscount
    const [usingCoins, setUsingCoins] = useState(false); // Tracks if user intends to use coins
    const [coinsToApply, setCoinsToApply] = useState(0); // Number of coins user wants to apply

    // Available coupons list
    const availableCoupons = [
        {
            code: 'FIRST10',
            description: '10% off on your first order',
            type: 'percentage',
            value: 10,
            icon: 'local-offer'
        },
        {
            code: 'MEAT50',
            description: 'Flat ₹50 off on your order',
            type: 'fixed',
            value: 50,
            icon: 'local-offer'
        }
    ];

    // Add these new state variables for the custom alert
    const [alertVisible, setAlertVisible] = useState(false);
    const [alertMessage, setAlertMessage] = useState('');
    const [alertType, setAlertType] = useState('success'); // 'success', 'error'
    const fadeAnim = useRef(new Animated.Value(0)).current;

    // Optimized user coins fetching
    useEffect(() => {
        if (currentUser) {
            getUserCoinsData();
        }
    }, []);

    // Refresh coins data when screen comes into focus
    React.useEffect(() => {
        const unsubscribe = navigation.addListener('focus', () => {
            if (currentUser) {
                getUserCoinsData();
            }
        });
        return unsubscribe;
    }, [navigation, currentUser]);

    // Add this function to show the custom alert
    const showAlert = (message, type = 'success') => {
        setAlertMessage(message);
        setAlertType(type);
        setAlertVisible(true);

        // Fade in
        Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
        }).start();

        // Hide after 2 seconds
        setTimeout(() => {
            Animated.timing(fadeAnim, {
                toValue: 0,
                duration: 300,
                useNativeDriver: true,
            }).start(() => {
                setAlertVisible(false);
            });
        }, 2000);
    };

    const calculateSubtotal = () => {
        return cartItems.reduce((sum, item) => sum + item.totalPrice, 0);
    };

    const calculateTotalSavings = () => {
        // Savings from coupon + potential coin discount only (removed item discounts)
        const potentialCoinsDiscount = usingCoins ? coinsToApply : 0; // Use coinsToApply for potential discount
        return couponDiscount + potentialCoinsDiscount;
    };

    const getFinalTotal = () => {
        const subtotal = calculateSubtotal();
        const deliveryFee = subtotal >= 499 ? 0 : 49;
        const potentialCoinsDiscount = usingCoins ? coinsToApply : 0; // Use coinsToApply for potential discount
        return subtotal + deliveryFee - couponDiscount - potentialCoinsDiscount;
    };

    // Helper function to get available coins from currentUser (kept for future use)
    // eslint-disable-next-line no-unused-vars
    const getAvailableCoins = () => {
        return currentUser?.activeCoins ?? currentUser?.coins ?? 0;
    };

    // Helper function to get total coins from currentUser
    const getTotalCoinsCount = () => {
        return currentUser?.totalCoins ?? currentUser?.coins ?? 0;
    };

    // Removed getUsedCoins and hasUsedCoins as they are less relevant now

    const applyCoupon = (coupon) => {
        // Check if user is already using coins
        if (usingCoins) {
            showAlert('You cannot use both coupons and coins. Please remove coins first.', 'error');
            return;
        }

        // Check if coupon has already been used by this user
        if (currentUser?.usedCoupons?.some(usedCoupon => usedCoupon.couponCode === coupon.code)) {
            showAlert('This coupon has already been used by you.', 'error');
            return;
        }

        // Simplified coupon logic without shipping coupon
        const subtotal = calculateSubtotal();
        let discount = 0;

        if (coupon.type === 'percentage') {
            discount = Math.round(subtotal * (coupon.value / 100)); // Percentage discount
        } else if (coupon.type === 'fixed') {
            discount = Math.min(coupon.value, subtotal); // Fixed amount discount
        }

        if (discount > 0) {
            setCouponDiscount(discount);
            setAppliedCoupon(coupon);
            showAlert(`Coupon applied! You saved ₹${discount}`);
        } else {
            showAlert('This coupon cannot be applied to your current order.', 'error');
        }
    };

    const removeCoupon = () => {
        setCouponDiscount(0);
        setAppliedCoupon(null);
        showAlert('Coupon removed successfully');
    };

    // Function to handle toggling coin usage
    const handleUseCoinsToggle = async () => {
        // Use the current state values instead of making an API call
        // This prevents unnecessary API calls that could cause loops
        const totalCoins = getTotalCoinsCount(); // Use totalCoins instead of availableCoins as requested
        const subtotal = calculateSubtotal();

        if (usingCoins) {
            // If currently using coins, toggle off
            setUsingCoins(false);
            setCoinsToApply(0);
            showAlert('Coins discount removed.');
        } else {
            // Check if user is already using a coupon
            if (Boolean(appliedCoupon)) {
                showAlert('You cannot use both coins and coupons. Please remove the coupon first.', 'error');
                return;
            }

            // Check minimum order amount for coins (₹100)
            if (subtotal < 100) {
                showAlert('Minimum order amount of ₹100 required to use coins.', 'error');
                return;
            }

            // If not using coins, toggle on
            if (totalCoins <= 0) {
                showAlert('You have no available coins to use.', 'error');
                return;
            }

            // Calculate max coins applicable with restrictions:
            // 1. Cannot exceed subtotal value
            // 2. Maximum 100 coins per order
            // 3. Cannot exceed available coins
            const maxApplicableCoins = Math.min(totalCoins, subtotal, 100);

            if (maxApplicableCoins <= 0) {
                 showAlert('Cart total is too low to apply coins.', 'error');
                 return;
            }

            setCoinsToApply(maxApplicableCoins);
            setUsingCoins(true);
            showAlert(`Applying ${maxApplicableCoins} coins (max 100 per order). Discount will be confirmed at checkout.`);
        }
    };


    const handleCheckout = () => {
        if (cartItems.length === 0) {
            Alert.alert('Empty Cart', 'Please add items to cart before checkout');
            return;
        }

        // Calculate all bill details
        const subtotal = calculateSubtotal();
        const deliveryFee = subtotal >= 499 ? 0 : 49;
        const totalSavings = calculateTotalSavings();
        const finalTotal = getFinalTotal();

        // Pass all bill details to checkout screen
        navigation.navigate('Checkout', {
            subtotal: subtotal,
            deliveryFee: deliveryFee,
            totalSavings: totalSavings,
            finalTotal: finalTotal,
            couponDiscount: couponDiscount,
            coinsToApply: usingCoins ? coinsToApply : 0, // Pass 0 if not using coins
            appliedCoupon: appliedCoupon,
            usingCoins: usingCoins
        });
    };

    // Update the handleQuantityChange function to match the HomeScreen logic
    const handleQuantityChange = (item, increment) => {
        const newQuantity = item.quantity + increment;
        if (newQuantity >= 1) {
            updateCartItemQuantity(item.id, newQuantity);
        } else {
            // If quantity becomes less than 1, remove the item from cart
            removeFromCart(item.id);
        }
    };

    // Update the quantity control

    if (!cartItems || cartItems.length === 0) {
        return (
            <View className="flex-1 bg-gray-50">
                <StatusBar barStyle="light-content" backgroundColor="#A31621" />

                {/* Simple Premium Header - Adjusted Height */}
                <View className="bg-madder h-28 rounded-b-3xl shadow-md">
                    <View className="flex-row justify-between items-center p-4 pt-10 h-full">
                        <View className="flex-row items-center gap-3">
                            <TouchableOpacity
                                className="bg-white/20 p-2 rounded-full"
                                onPress={() => navigation.goBack()}
                            >
                                <Ionicons name="arrow-back" size={22} color="white" />
                            </TouchableOpacity>
                            <Text className="text-xl text-white font-bold">My Cart</Text>
                        </View>
                    </View>
                </View>

                {/* Empty cart view - Added top margin */}
                <View className="flex-1 justify-center items-center p-4 mt-3">
                    <View className="bg-madder/10 p-6 rounded-full mb-4">
                        <Ionicons name="cart-outline" size={80} color="#A31621" />
                    </View>
                    <Text className="text-xl text-gray-800 font-bold mt-6 text-center">
                        Your cart is empty
                    </Text>
                    <Text className="text-gray-500 mt-2 text-center px-10">
                        Looks like you haven't added anything to your cart yet
                    </Text>
                    <TouchableOpacity
                        className="mt-8 bg-madder px-8 py-3 rounded-lg shadow-md"
                        onPress={() => navigation.navigate('Home')}
                    >
                        <Text className="text-white font-bold">Start Shopping</Text>
                    </TouchableOpacity>
                </View>
            </View>
        );
    }

    // Inside the return statement, update the Offers & Benefits section
    return (
        <View className="flex-1 bg-gray-50">
            <StatusBar barStyle="light-content" backgroundColor="#A31621" />

            {/* Simple Premium Header - Adjusted Height */}
            <View className="bg-madder h-28 rounded-b-3xl shadow-md">
                <View className="flex-row justify-between items-center p-4 pt-10 h-full">
                    <View className="flex-row items-center gap-3">
                        <TouchableOpacity
                            className="bg-white/20 p-2 rounded-full"
                            onPress={() => navigation.goBack()}
                        >
                            <Ionicons name="arrow-back" size={22} color="white" />
                        </TouchableOpacity>
                        <Text className="text-xl text-white font-bold">My Cart</Text>
                    </View>
                    <TouchableOpacity
                        onPress={clearCart}
                        className="flex-row items-center bg-white/20 px-3 py-2 rounded-full"
                    >
                        <Ionicons name="trash-outline" size={18} color="#fff" />
                        <Text className="text-white ml-1 text-sm font-medium">Clear</Text>
                    </TouchableOpacity>
                </View>
            </View>

            {/* Modern Cart Summary Card - Added Space */}
            <View className="bg-white mx-4 p-4 rounded-2xl shadow-md mt-3 z-10 border border-gray-100">
                <View className="flex-row justify-between items-center">
                    <View className="flex-row items-center">
                        <View className="w-10 h-10 bg-madder/10 rounded-full items-center justify-center mr-3">
                            <Ionicons name="cart" size={18} color="#A31621" />
                        </View>
                        <View>
                            <Text className="text-base text-gray-800 font-bold">My Shopping Bag</Text>
                            <Text className="text-gray-500 text-xs mt-0.5">
                                {cartItems.length} {cartItems.length === 1 ? 'item' : 'items'} selected
                            </Text>
                        </View>
                    </View>
                    <View className="bg-madder/10 px-3 py-1.5 rounded-full">
                        <Text className="text-madder font-medium text-sm">₹{calculateSubtotal()}</Text>
                    </View>
                </View>
            </View>

            {/* Main Content */}
            <ScrollView
                className="flex-1 mx-4 mt-4"
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{ paddingBottom: 120 }}
            >
                {cartItems.map((item) => (
                    <View
                        key={`cart-item-${item.id}`}
                        className="bg-white mb-4 rounded-2xl overflow-hidden shadow-sm border border-gray-100"
                    >
                        <View className="p-4">
                            {/* Image and Basic Info - Modern Design */}
                            <View className="flex-row mb-3">
                                <Image
                                    source={item.image ?
                                        typeof item.image === 'string' ?
                                            { uri: item.image } : item.image
                                        : require('../assets/logo.png')}
                                    className="w-20 h-20 rounded-lg"
                                    resizeMode="cover"
                                />
                                <View className="flex-1 ml-3 justify-between">
                                    <View>
                                        <View className="flex-row justify-between items-start">
                                            <View className="flex-1 pr-2">
                                                <Text className="text-base font-bold text-gray-800" numberOfLines={1}
                                                    ellipsizeMode="tail">
                                                    {item.name}
                                                </Text>
                                                <Text className="text-gray-500 text-xs mt-0.5" numberOfLines={1}>
                                                    {item.desc}
                                                </Text>

                                                <View className="mt-1.5 flex-row items-center">
                                                    <View className="bg-gray-100 px-2 py-0.5 rounded-md self-start">
                                                        <Text className="text-xs text-gray-700">
                                                            {item.selectedWeight}
                                                        </Text>
                                                    </View>

                                                    {item.savings > 0 && (
                                                        <View className="ml-2 bg-green-50 px-2 py-0.5 rounded-md">
                                                            <Text className="text-green-600 text-xs">
                                                                Save ₹{item.savings}
                                                            </Text>
                                                        </View>
                                                    )}
                                                </View>
                                            </View>
                                            <TouchableOpacity
                                                onPress={() => removeFromCart(item.id)}
                                                className="p-1.5 bg-gray-100 rounded-full"
                                            >
                                                <Ionicons name="trash-outline" size={16} color="#A31621" />
                                            </TouchableOpacity>
                                        </View>
                                    </View>
                                </View>
                            </View>

                            {/* Price and Quantity Controls in a separate row */}
                            <View className="flex-row justify-between items-center pt-2 border-t border-gray-100">
                                <View>
                                    <Text className="text-base font-bold text-madder">
                                        ₹{item.totalPrice}
                                    </Text>
                                </View>

                                {/* Sleek Quantity controls */}
                                <View className="flex-row items-center bg-gray-100 rounded-lg overflow-hidden">
                                    <TouchableOpacity
                                        onPress={() => handleQuantityChange(item, -1)}
                                        className="w-8 h-8 items-center justify-center"
                                    >
                                        <Ionicons name="remove" size={18} color="#A31621" />
                                    </TouchableOpacity>

                                    <View className="bg-madder px-2.5 py-1">
                                        <Text className="text-white text-xs font-bold">{item.quantity}</Text>
                                    </View>

                                    <TouchableOpacity
                                        onPress={() => handleQuantityChange(item, 1)}
                                        className="w-8 h-8 items-center justify-center"
                                        disabled={item.quantity >= 10}
                                    >
                                        <Ionicons name="add" size={18} color={item.quantity >= 10 ? "#999" : "#A31621"} />
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </View>
                ))}

                {/* Modern Promo Codes Section */}
                <View className="bg-white rounded-2xl p-4 mb-4 shadow-sm border border-gray-100">
                    <View className="flex-row justify-between items-center mb-3">
                        <View className="flex-row items-center">
                            <View className="w-8 h-8 bg-indigo-100 rounded-full items-center justify-center">
                                <Ionicons name="ticket-outline" size={16} color="#6366F1" />
                            </View>
                            <Text className="text-base font-bold text-gray-800 ml-2">Promo Codes</Text>
                        </View>
                        {Boolean(appliedCoupon) && (
                            <TouchableOpacity
                                className="bg-gray-100 px-2 py-1 rounded-md"
                                onPress={removeCoupon}
                            >
                                <Text className="font-medium text-xs text-madder">Remove</Text>
                            </TouchableOpacity>
                        )}
                    </View>

                    {/* Applied Coupon Display */}
                    {Boolean(appliedCoupon) && (
                        <View className="bg-green-50 p-3 rounded-lg flex-row items-center mb-3 border border-green-100">
                            <View className="w-8 h-8 bg-green-100 rounded-full items-center justify-center">
                                <Ionicons name="checkmark" size={16} color="#10B981" />
                            </View>
                            <View className="ml-2 flex-1">
                                <Text className="text-gray-800 font-medium text-sm">{appliedCoupon.code}</Text>
                                <Text className="text-green-600 text-xs">{appliedCoupon.description}</Text>
                            </View>
                            <View className="bg-green-100 px-2 py-1 rounded-md">
                                <Text className="text-green-700 font-medium text-xs">-₹{couponDiscount}</Text>
                            </View>
                        </View>
                    )}

                    {/* Modern Coupon Cards */}
                    <View>
                        {availableCoupons.map((coupon, index) => {
                            const isUsed = Boolean(currentUser?.usedCoupons?.some(usedCoupon => usedCoupon.couponCode === coupon.code));
                            const isApplied = Boolean(appliedCoupon?.code === coupon.code);
                            const isDisabled = Boolean(appliedCoupon !== null || isUsed || usingCoins);

                            return (
                                <TouchableOpacity
                                    key={`coupon-${index}`}
                                    className={`border rounded-lg p-3 flex-row items-center ${index > 0 ? 'mt-2' : ''} ${
                                        isApplied
                                            ? 'border-green-500 bg-green-50'
                                            : isUsed
                                            ? 'border-gray-300 bg-gray-50'
                                            : usingCoins
                                            ? 'border-gray-200 bg-gray-50'
                                            : 'border-gray-100'
                                    }`}
                                    onPress={() => applyCoupon(coupon)}
                                    disabled={isDisabled}
                                >
                                <View className={`w-10 h-10 rounded-full items-center justify-center ${
                                    coupon.type === 'percentage' ? 'bg-indigo-100' :
                                    coupon.type === 'fixed' ? 'bg-amber-100' : 'bg-blue-100'
                                }`}>
                                    <Ionicons name={
                                        coupon.icon === 'local-offer' ? 'pricetag' :
                                        coupon.icon === 'delivery-dining' ? 'bicycle' : 'gift'
                                    } size={16} color={
                                        coupon.type === 'percentage' ? '#6366F1' :
                                        coupon.type === 'fixed' ? '#F59E0B' : '#3B82F6'
                                    } />
                                </View>
                                <View className="flex-1 ml-2">
                                    <Text className={`font-medium text-sm ${isUsed ? 'text-gray-500' : 'text-gray-800'}`}>
                                        {coupon.code}
                                    </Text>
                                    <Text className={`text-xs ${isUsed ? 'text-gray-400' : 'text-gray-600'}`}>
                                        {isUsed ? 'Already used' : coupon.description}
                                    </Text>
                                    {usingCoins && !isUsed && !isApplied && (
                                        <Text className="text-orange-500 text-xs mt-0.5">
                                            Remove coins to use coupon
                                        </Text>
                                    )}
                                </View>
                                {isApplied ? (
                                    <Ionicons name="checkmark-circle" size={18} color="#10B981" />
                                ) : isUsed ? (
                                    <View className="bg-gray-200 w-6 h-6 rounded-full items-center justify-center">
                                        <Ionicons name="checkmark" size={14} color="#9CA3AF" />
                                    </View>
                                ) : usingCoins ? (
                                    <View className="bg-gray-100 w-6 h-6 rounded-full items-center justify-center">
                                        <Ionicons name="lock-closed" size={14} color="#9CA3AF" />
                                    </View>
                                ) : (
                                    <View className="bg-gray-100 w-6 h-6 rounded-full items-center justify-center">
                                        <Ionicons name="chevron-forward" size={14} color="#9CA3AF" />
                                    </View>
                                )}
                            </TouchableOpacity>
                            );
                        })}
                    </View>
                </View>

                {/* Premium Rewards Hub */}
                <View className="bg-white rounded-2xl p-4 mb-4 shadow-sm border border-gray-100">
                    <View className="flex-row items-center mb-3">
                        <View className="w-8 h-8 bg-amber-100 rounded-full items-center justify-center">
                            <Ionicons name="star" size={16} color="#F59E0B" />
                        </View>
                        <Text className="text-base font-bold text-gray-800 ml-2">Rewards Hub</Text>
                    </View>

                    <View>
                        {/* Reward Coins Card - Premium Design */}
                        <View className={`rounded-lg p-3 border ${
                            Boolean(appliedCoupon) ? 'bg-gray-50 border-gray-200' : 'bg-amber-50 border-amber-100'
                        }`}>
                            <View className="flex-row items-center justify-between">
                                <View className="flex-row items-center">
                                    <View className={`w-10 h-10 rounded-full items-center justify-center ${
                                        Boolean(appliedCoupon) ? 'bg-gray-200' : 'bg-amber-200'
                                    }`}>
                                        <Ionicons name="wallet" size={18} color={Boolean(appliedCoupon) ? "#9CA3AF" : "#F59E0B"} />
                                    </View>
                                    <View className="ml-2">
                                        <Text className={`font-medium text-sm ${Boolean(appliedCoupon) ? 'text-gray-500' : 'text-gray-800'}`}>
                                            Fresh Coin
                                        </Text>
                                        <Text className={`text-xs ${Boolean(appliedCoupon) ? 'text-gray-400' : 'text-amber-700'}`}>
                                            {getTotalCoinsCount()} coins (₹{getTotalCoinsCount()} value)
                                        </Text>

                                        {usingCoins && (
                                            <Text className="text-green-600 text-xs font-medium mt-0.5">
                                                Using ₹{coinsToApply} as discount (max 100)
                                            </Text>
                                        )}

                                        {Boolean(appliedCoupon) && (
                                            <Text className="text-orange-500 text-xs mt-0.5">
                                                Remove coupon to use coins
                                            </Text>
                                        )}

                                        {!Boolean(appliedCoupon) && !usingCoins && calculateSubtotal() < 100 && (
                                            <Text className="text-orange-500 text-xs mt-0.5">
                                                Minimum ₹100 order required
                                            </Text>
                                        )}
                                    </View>
                                </View>

                                <TouchableOpacity
                                    className={`px-3 py-1.5 rounded-lg ${
                                        usingCoins
                                            ? 'bg-gray-200'
                                            : Boolean(appliedCoupon) || getTotalCoinsCount() <= 0 || calculateSubtotal() < 100
                                            ? 'bg-gray-300'
                                            : 'bg-amber-500'
                                    }`}
                                    onPress={handleUseCoinsToggle}
                                    disabled={Boolean(!usingCoins && (getTotalCoinsCount() <= 0 || Boolean(appliedCoupon) || calculateSubtotal() < 100))}
                                >
                                    <Text className={`font-medium text-xs ${
                                        usingCoins ? 'text-gray-700' :
                                        getTotalCoinsCount() <= 0 ? 'text-white/50' : 'text-white'
                                    }`}>
                                        {usingCoins ? 'Remove' : 'Apply Coins'}
                                    </Text>
                                </TouchableOpacity>
                            </View>
                        </View>

                        {/* Benefits Cards - Sleek Design */}
                        <View className="flex-row mt-3">
                            <View className="flex-1 bg-blue-50 rounded-lg p-3 border border-blue-100 mr-2">
                                <View className="flex-row items-center mb-1">
                                    <View className="w-7 h-7 bg-blue-100 rounded-full items-center justify-center mr-2">
                                        <Ionicons name="bicycle" size={14} color="#3B82F6" />
                                    </View>
                                    <Text className="text-gray-800 font-medium text-sm">Free Delivery</Text>
                                </View>
                                <Text className="text-blue-700 text-xs">Orders above ₹499</Text>
                                {calculateSubtotal() >= 499 && (
                                    <View className="bg-blue-100 px-2 py-0.5 rounded-md self-start mt-1">
                                        <Text className="text-blue-700 text-xs">Applied</Text>
                                    </View>
                                )}
                            </View>

                            <View className="flex-1 bg-purple-50 rounded-lg p-3 border border-purple-100">
                                <View className="flex-row items-center mb-1">
                                    <View className="w-7 h-7 bg-purple-100 rounded-full items-center justify-center mr-2">
                                        <Ionicons name="cash" size={14} color="#8B5CF6" />
                                    </View>
                                    <Text className="text-gray-800 font-medium text-sm">Earn Coins</Text>
                                </View>
                                <Text className="text-purple-700 text-xs">Earn 10 coins for every ₹100 you spend!</Text>

                            </View>
                        </View>
                    </View>
                </View>

                {/* Order Summary - Modern Design (Matching CheckoutScreen) */}
                <View className="bg-white rounded-2xl p-4 shadow-sm mb-4 border border-gray-100">
                    <View className="flex-row items-center mb-3">
                        <View className="w-8 h-8 bg-gray-100 rounded-full items-center justify-center mr-2">
                            <Ionicons name="receipt-outline" size={16} color="#4B5563" />
                        </View>
                        <Text className="text-base font-bold text-gray-800">Bill Details</Text>
                    </View>

                    <View className="border-b border-gray-100 pb-3 mb-3">
                        <Text className="font-medium text-sm mb-2 text-gray-800">Items ({cartItems.length})</Text>
                        {cartItems.map((item, index) => (
                            <View
                                key={`cart-item-summary-${item.id}-${index}`}
                                className="flex-row items-center mb-1.5"
                            >
                                <Text className="text-gray-600 flex-1 text-xs">
                                    {item.name} ({item.selectedWeight} × {item.quantity})
                                </Text>
                                <Text className="font-medium text-xs">₹{item.totalPrice}</Text>
                            </View>
                        ))}
                    </View>

                    <View>
                        <View className="flex-row justify-between items-center">
                            <Text className="text-gray-600 text-xs">Item Total</Text>
                            <Text className="font-medium text-xs text-gray-800">₹{calculateSubtotal()}</Text>
                        </View>

                        <View className="flex-row justify-between items-center my-2">
                            <View className="flex-row items-center">
                                <View className="w-6 h-6 bg-blue-50 rounded-full items-center justify-center mr-2">
                                    <Ionicons name="bicycle" size={14} color="#3B82F6" />
                                </View>
                                <Text className="text-gray-600 text-xs">Delivery Fee</Text>
                            </View>
                            {calculateSubtotal() >= 499 ? (
                                <View className="flex-row items-center">
                                    <Text className="font-medium line-through text-gray-400 text-xs mr-1">
                                        ₹49
                                    </Text>
                                    <View className="bg-blue-50 px-2 py-0.5 rounded-md">
                                        <Text className="font-medium text-blue-700 text-xs">FREE</Text>
                                    </View>
                                </View>
                            ) : (
                                <Text className="font-medium text-xs text-gray-800">
                                    ₹49
                                </Text>
                            )}
                        </View>

                        {couponDiscount > 0 && (
                            <View className="flex-row justify-between items-center">
                                <View className="flex-row items-center">
                                    <View className="w-6 h-6 bg-indigo-50 rounded-full items-center justify-center mr-2">
                                        <Ionicons name="pricetag" size={14} color="#6366F1" />
                                    </View>
                                    <Text className="text-gray-600 text-xs">
                                        Promo Code {Boolean(appliedCoupon) ? `(${appliedCoupon.code})` : ''}
                                    </Text>
                                </View>
                                <Text className="text-green-600 font-medium text-xs">
                                    -₹{couponDiscount}
                                </Text>
                            </View>
                        )}

                        {/* Display potential coins discount */}
                        {usingCoins && coinsToApply > 0 && (
                            <View className="flex-row justify-between items-center">
                                <View className="flex-row items-center">
                                    <View className="w-6 h-6 bg-amber-50 rounded-full items-center justify-center mr-2">
                                        <Ionicons name="wallet" size={14} color="#F59E0B" />
                                    </View>
                                    <Text className="text-gray-600 text-xs">Reward Coins</Text>
                                </View>
                                <Text className="text-green-600 font-medium text-xs">
                                    -₹{coinsToApply}
                                </Text>
                            </View>
                        )}

                        <View className="h-px bg-gray-100 my-2" />

                        <View className="flex-row justify-between items-center">
                            <Text className="text-gray-800 font-bold text-sm">To Pay</Text>
                            <Text className="font-bold text-madder text-base">
                                ₹{getFinalTotal()}
                            </Text>
                        </View>

                        {calculateTotalSavings() > 0 && (
                            <View className="bg-green-50 p-2 rounded-lg mt-2 border border-green-100">
                                <View className="flex-row items-center">
                                    <Ionicons name="shield-checkmark" size={14} color="#10B981" />
                                    <Text className="text-green-700 ml-1.5 text-xs font-medium">
                                        Total savings of ₹{calculateTotalSavings()} on this order
                                    </Text>
                                </View>
                            </View>
                        )}
                    </View>
                </View>
            </ScrollView>

            {/* Modern Checkout Button - Positioned above navigation bar */}
            <View className="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-100 shadow-lg"
                  style={{
                      elevation: 10,
                      zIndex: 1000,
                      backgroundColor: 'white',
                      borderTopWidth: 1,
                      borderTopColor: '#F3F4F6',
                      paddingBottom: 20,
                      paddingTop: 16,
                      paddingHorizontal: 16
                  }}>
                <View className="flex-row items-center justify-between">
                    <View className="flex-1 mr-4">
                        <Text className="text-gray-500 text-xs mb-1">Total Amount</Text>
                        <View className="flex-row items-center flex-wrap">
                            <Text className="text-xl font-bold text-gray-800 mr-2">
                                ₹{getFinalTotal()}
                            </Text>
                            {/* Update savings calculation */}
                            {calculateTotalSavings() > 0 && (
                                <View className="bg-green-50 px-2 py-1 rounded-md border border-green-100">
                                    <Text className="text-green-700 text-xs font-medium">
                                        Saved ₹{calculateTotalSavings()}
                                    </Text>
                                </View>
                            )}
                        </View>
                    </View>

                    <TouchableOpacity
                        onPress={handleCheckout}
                        className="bg-madder px-6 py-3 rounded-xl flex-row items-center shadow-sm"
                        style={{ minWidth: 160 }}
                    >
                        <Text className="text-white font-semibold text-sm mr-2">Proceed to Checkout</Text>
                        <Ionicons name="chevron-forward" size={18} color="white" />
                    </TouchableOpacity>
                </View>
            </View>

            {/* Modern Toast Alert Overlay */}
            {alertVisible && (
                <Animated.View
                    style={{
                        opacity: fadeAnim,
                        transform: [{
                            translateY: fadeAnim.interpolate({
                                inputRange: [0, 1],
                                outputRange: [20, 0]
                            })
                        }],
                        zIndex: 2000
                    }}
                    className={`absolute bottom-24 left-4 right-4 p-3 rounded-lg shadow-sm flex-row items-center ${
                        alertType === 'success'
                            ? 'bg-white border border-gray-100'
                            : 'bg-white border border-red-100'
                    }`}
                >
                    <View className={`w-8 h-8 rounded-full items-center justify-center ${
                        alertType === 'success' ? 'bg-madder/10' : 'bg-red-50'
                    }`}>
                        <Ionicons
                            name={alertType === 'success' ? 'checkmark' : 'alert'}
                            size={16}
                            color={alertType === 'success' ? '#A31621' : '#EF4444'}
                        />
                    </View>
                    <Text
                        className={`ml-2 text-sm flex-1 ${
                            alertType === 'success' ? 'text-gray-700' : 'text-red-700'
                        }`}
                    >
                        {alertMessage}
                    </Text>
                    <TouchableOpacity
                        onPress={() => {
                            Animated.timing(fadeAnim, {
                                toValue: 0,
                                duration: 300,
                                useNativeDriver: true,
                            }).start(() => {
                                setAlertVisible(false);
                            });
                        }}
                        className="p-1"
                    >
                        <Ionicons name="close" size={16} color="#9CA3AF" />
                    </TouchableOpacity>
                </Animated.View>
            )}
        </View>
    );
};

export default CartScreen;
