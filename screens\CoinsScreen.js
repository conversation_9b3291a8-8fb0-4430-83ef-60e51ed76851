import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, TouchableOpacity, FlatList, ActivityIndicator, Alert, Image } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useUser } from '../context/UserContext';
import { useOrders } from '../context/OrderContext';
import { getUserCoins } from '../utils/api/userProfileApi';

const CoinsScreen = () => {
    const navigation = useNavigation();
    const { currentUser, refreshUserData, restoreCoins } = useUser();
    const { orders, fetchUserOrders } = useOrders();

    // State for coin history and loading
    const [coinHistory, setCoinHistory] = useState([]);
    const [loading, setLoading] = useState(true);
    const [totalCoins, setTotalCoins] = useState(0);
    const [availableCoins, setAvailableCoins] = useState(0);
    const [usedCoins, setUsedCoins] = useState(0);

    // Fetch coin data directly from API when screen is focused
    useFocusEffect(
        useCallback(() => {
            const fetchData = async () => {
                try {
                    setLoading(true);
                    console.log('Fetching coin data for coins screen...');

                    // Fetch coin data directly from API with cache-busting timestamp
                    const timestamp = new Date().getTime();
                    const coinData = await getUserCoins(timestamp);

                    // Set coin totals from API response
                    setTotalCoins(coinData.totalCoins || 0);
                    setAvailableCoins(coinData.activeCoins || 0);
                    setUsedCoins(coinData.usedCoins || 0);

                    // Process coin history from API
                    if (coinData.coinsHistory && Array.isArray(coinData.coinsHistory)) {
                        // Map the API coin history to our UI format
                        const history = coinData.coinsHistory.map(coin => {
                            // Determine the type based on the transaction type
                            let type = 'earned';
                            if (coin.type === 'USED') type = 'redeemed';
                            if (coin.type === 'REFUNDED') type = 'refunded';
                            if (coin.type === 'EXPIRED') type = 'expired';

                            return {
                                id: `${type}-${coin._id || coin.orderId || Math.random().toString(36).substring(7)}`,
                                type: type,
                                amount: Math.abs(coin.amount), // Use absolute value for display
                                description: coin.description || `${type.charAt(0).toUpperCase() + type.slice(1)} coins`,
                                date: coin.date || new Date().toISOString(),
                                expiry: coin.expiry,
                                orderNumber: coin.orderNumber,
                                orderId: coin.orderId
                            };
                        }).sort((a, b) => new Date(b.date) - new Date(a.date)); // Sort by date, newest first

                        setCoinHistory(history);
                        console.log('Coin history processed from API:', history.length, 'entries');
                    } else {
                        console.log('No coin history available from API');
                        setCoinHistory([]);
                    }

                    console.log('Coin data fetched successfully');
                } catch (error) {
                    console.error('Error fetching data for coins screen:', error);
                    // Set default values on error
                    setTotalCoins(currentUser?.coins || 0);
                    setAvailableCoins(currentUser?.coins || 0);
                    setUsedCoins(currentUser?.usedCoins || 0);
                    setCoinHistory([]);
                } finally {
                    setLoading(false);
                }
            };

            fetchData();

            return () => {
                // Cleanup function when screen loses focus
            };
        }, [])
    );

    // We're already fetching coin data in useFocusEffect, so we don't need a separate useEffect

    const renderCoinHistoryItem = ({ item }) => {
        // Determine icon and colors based on transaction type
        let iconName = 'add-circle';
        let iconColor = '#16A34A';
        let bgColor = 'bg-green-100';
        let textColor = 'text-green-600';
        let statusText = 'Available';
        let statusBgColor = 'bg-green-100';
        let statusTextColor = 'text-green-800';

        if (item.type === 'redeemed') {
            iconName = 'remove-circle';
            iconColor = '#DC2626';
            bgColor = 'bg-red-100';
            textColor = 'text-red-600';
            statusText = 'Used';
            statusBgColor = 'bg-red-100';
            statusTextColor = 'text-red-800';
        } else if (item.type === 'refunded') {
            iconName = 'replay';
            iconColor = '#9333EA';
            bgColor = 'bg-purple-100';
            textColor = 'text-purple-600';
            statusText = 'Refunded';
            statusBgColor = 'bg-purple-100';
            statusTextColor = 'text-purple-800';
        } else if (item.type === 'expired') {
            iconName = 'timer-off';
            iconColor = '#9CA3AF';
            bgColor = 'bg-gray-100';
            textColor = 'text-gray-600';
            statusText = 'Expired';
            statusBgColor = 'bg-gray-100';
            statusTextColor = 'text-gray-800';
        }

        // Format date
        const formattedDate = new Date(item.date).toLocaleDateString();

        return (
            <View className="bg-white rounded-xl p-4 mb-4 shadow-sm">
                <View className="flex-row justify-between items-center">
                    <View className="flex-row items-center">
                        <View className={`w-10 h-10 rounded-full ${bgColor} items-center justify-center mr-3`}>
                            <MaterialIcons
                                name={iconName}
                                size={20}
                                color={iconColor}
                            />
                        </View>
                        <View>
                            <Text className="text-gray-800 font-bold">
                                {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
                            </Text>
                            <Text className="text-gray-500 text-xs">{formattedDate}</Text>
                        </View>
                    </View>

                    <View>
                        <Text className={`text-lg font-bold ${textColor}`}>
                            {item.type === 'earned' ? '+' :
                             item.type === 'refunded' ? '+' : '-'}{item.amount}
                        </Text>
                    </View>
                </View>

                <Text className="text-gray-600 mt-2 ml-13">{item.description}</Text>

                {/* Show status badges */}
                <View className="flex-row mt-2 ml-13">
                    {/* Status badge */}
                    <View className={`${statusBgColor} px-2 py-0.5 rounded-full mr-2`}>
                        <Text className={`${statusTextColor} text-xs font-medium`}>{statusText}</Text>
                    </View>

                    {/* Expiry date for earned coins */}
                    {item.type === 'earned' && item.expiry && (
                        <View className="bg-amber-50 px-2 py-0.5 rounded-full mr-2">
                            <Text className="text-amber-800 text-xs">
                                Expires: {new Date(item.expiry).toLocaleDateString()}
                            </Text>
                        </View>
                    )}

                    {/* Order number if available */}
                    {item.orderNumber && (
                        <View className="bg-gray-100 px-2 py-0.5 rounded-full">
                            <Text className="text-gray-600 text-xs">Order #{item.orderNumber}</Text>
                        </View>
                    )}
                </View>
            </View>
        );
    };

    return (
        <View className="flex-1 bg-snow">
            <View className="bg-madder p-6 pt-16 rounded-b-3xl pb-6 flex-row items-center">
                <TouchableOpacity onPress={() => navigation.goBack()} className="mr-4">
                    <MaterialIcons name="arrow-back" size={24} color="white" />
                </TouchableOpacity>
                <Text className="text-xl text-white font-bold">My Coins</Text>
            </View>

            <View className="p-4 flex-1">
                {loading ? (
                    // Loading state
                    <View className="flex-1 items-center justify-center">
                        <ActivityIndicator size="large" color="#A31621" />
                        <Text className="text-gray-600 mt-4">Loading your coins...</Text>
                    </View>
                ) : (
                    <>
                        {/* Banner Image */}
                        <View
                            className="rounded-xl overflow-hidden mb-4"
                            style={{
                                shadowColor: '#000',
                                shadowOffset: { width: 2, height: 2 },
                                shadowOpacity: 0.1,
                                shadowRadius: 3,
                                elevation: 3,
                                backgroundColor: 'white'
                            }}
                        >
                            <Image
                                source={require('../assets/banner7.png')}
                                style={{
                                    width: '100%',
                                    height: undefined,
                                    aspectRatio: 1094/157
                                }}
                                resizeMode="contain"
                            />
                        </View>

                        {/* Coin Balance Card */}
                        <View className="bg-white rounded-xl p-6 mb-6 shadow-sm items-center">
                            <View className="w-20 h-20 rounded-full bg-madder/10 items-center justify-center mb-3">
                                <MaterialIcons name="monetization-on" size={40} color="#A31621" />
                            </View>

                            {/* Total Coins */}
                            <Text className="text-3xl font-bold text-gray-800 mb-1">{totalCoins}</Text>
                            <Text className="text-gray-500">Available Coins</Text>

                            {/* Available and Used Coins */}
                            <View className="flex-row justify-between w-full mt-4">
                                <View className="items-center flex-1">
                                    <Text className="text-xl font-bold text-green-600">{availableCoins}</Text>
                                    <Text className="text-gray-500 text-xs">Total Coins</Text>
                                </View>

                                <View className="items-center flex-1">
                                    <Text className="text-xl font-bold text-amber-600">{usedCoins}</Text>
                                    <Text className="text-gray-500 text-xs">Used Coins</Text>
                                </View>
                            </View>

                            {/* Used Coins Status */}
                            {usedCoins > 0 && (
                                <View className="bg-amber-50 p-3 rounded-lg mt-4">
                                    <Text className="text-amber-800 text-sm text-center">
                                        You have {usedCoins} coins that are currently marked as used.
                                        Once coins are used, they cannot be used again.
                                    </Text>
                                </View>
                            )}

                            <View className="w-full mt-6 pt-4 border-t border-gray-100">
                                <Text className="text-gray-600 text-center">
                                    Use your coins to get discounts on your orders. Every coin = ₹1 discount.
                                </Text>

                            </View>
                        </View>

                        {/* Coin History Section */}
                        <View className="flex-row justify-between items-center mb-4">
                            <Text className="text-lg font-bold text-gray-800">Coin History</Text>
                            <TouchableOpacity
                                className="bg-madder/10 px-3 py-1 rounded-full"
                                onPress={async () => {
                                    setLoading(true);
                                    try {
                                        // Fetch coin data directly from API with cache-busting timestamp
                                        const timestamp = new Date().getTime();
                                        const coinData = await getUserCoins(timestamp);

                                        // Set coin totals from API response
                                        setTotalCoins(coinData.totalCoins || 0);
                                        setAvailableCoins(coinData.activeCoins || 0);
                                        setUsedCoins(coinData.usedCoins || 0);

                                        // Process coin history from API
                                        if (coinData.coinsHistory && Array.isArray(coinData.coinsHistory)) {
                                            // Map the API coin history to our UI format
                                            const history = coinData.coinsHistory.map(coin => {
                                                // Determine the type based on the transaction type
                                                let type = 'earned';
                                                if (coin.type === 'USED') type = 'redeemed';
                                                if (coin.type === 'REFUNDED') type = 'refunded';
                                                if (coin.type === 'EXPIRED') type = 'expired';

                                                return {
                                                    id: `${type}-${coin._id || coin.orderId || Math.random().toString(36).substring(7)}`,
                                                    type: type,
                                                    amount: Math.abs(coin.amount), // Use absolute value for display
                                                    description: coin.description || `${type.charAt(0).toUpperCase() + type.slice(1)} coins`,
                                                    date: coin.date || new Date().toISOString(),
                                                    expiry: coin.expiry,
                                                    orderNumber: coin.orderNumber,
                                                    orderId: coin.orderId
                                                };
                                            }).sort((a, b) => new Date(b.date) - new Date(a.date)); // Sort by date, newest first

                                            setCoinHistory(history);
                                        } else {
                                            setCoinHistory([]);
                                        }
                                    } catch (error) {
                                        console.error('Error refreshing data:', error);
                                        Alert.alert('Error', 'Failed to refresh coin data');
                                    } finally {
                                        setLoading(false);
                                    }
                                }}
                            >
                                <Text className="text-madder font-medium">Refresh</Text>
                            </TouchableOpacity>
                        </View>

                        {/* Use FlatList with extra padding */}
                        {coinHistory.length > 0 ? (
                            <FlatList
                                data={coinHistory}
                                renderItem={renderCoinHistoryItem}
                                keyExtractor={item => item.id}
                                showsVerticalScrollIndicator={false}
                                contentContainerStyle={{ paddingBottom: 20 }}
                                ListFooterComponent={<View style={{ height: 20 }} />}
                                ListEmptyComponent={
                                    <View className="flex-1 items-center justify-center py-10">
                                        <View className="w-16 h-16 rounded-full bg-gray-100 items-center justify-center mb-4">
                                            <MaterialIcons name="history" size={30} color="#9CA3AF" />
                                        </View>
                                        <Text className="text-gray-500 text-center">No coin transactions yet</Text>
                                    </View>
                                }
                            />
                        ) : (
                            <View className="flex-1 items-center justify-center py-10">
                                <View className="w-16 h-16 rounded-full bg-gray-100 items-center justify-center mb-4">
                                    <MaterialIcons name="history" size={30} color="#9CA3AF" />
                                </View>
                                <Text className="text-gray-500 text-center">No coin transactions yet</Text>
                                <Text className="text-gray-400 text-center mt-2">Complete orders to earn coins</Text>
                            </View>
                        )}
                    </>
                )}
            </View>
        </View>
    );
};

export default CoinsScreen;
