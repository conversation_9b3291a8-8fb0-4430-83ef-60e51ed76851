import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
    Image,
    KeyboardAvoidingView,
    Platform,
    Animated,
    ActivityIndicator,
    StatusBar,
    Vibration,
    Keyboard,
    Alert
} from 'react-native';
import React, { useState, useRef, useEffect } from 'react';
import { MaterialIcons, Ionicons, AntDesign } from "@expo/vector-icons";

import { useAuth } from '../context/AuthContext';
import axios from 'axios';
import { API_URL } from '../config/constants';
import { storeAuthData } from '../utils/authStorage';
import { setupOtpNotificationListener, areNotificationsEnabled, showNotificationSetupGuidance } from '../utils/notificationUtils';
// Autofill permission system removed for better UX

const OTPScreen = ({ route, navigation }) => {
    const [otp, setOtp] = useState(['', '', '', '', '', '']);
    const [timer, setTimer] = useState(30);
    const [canResend, setCanResend] = useState(false);
    const [loading, setLoading] = useState(false);
    const [verificationSuccess, setVerificationSuccess] = useState(false);
    const inputRefs = useRef([]);
    const timerRef = useRef(null);
    const phoneNumber = route.params?.phoneNumber || '';
    const pushNotificationSent = route.params?.pushNotificationSent || false;
    const hasPushToken = route.params?.hasPushToken || false;
    const [alertMessage, setAlertMessage] = useState('');
    const [alertType, setAlertType] = useState('success');
    const [showPopup, setShowPopup] = useState(false);
    const [notificationsEnabled, setNotificationsEnabled] = useState(false);
    const [resendCount, setResendCount] = useState(0);
    const [resendCooldown, setResendCooldown] = useState(0);
    const [keyboardVisible, setKeyboardVisible] = useState(false);

    // OTP validation limiting states
    const [wrongOtpCount, setWrongOtpCount] = useState(0);
    const [otpCooldown, setOtpCooldown] = useState(0);
    const [isOtpBlocked, setIsOtpBlocked] = useState(false);

    // Animated values
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const successAnimation = useRef(new Animated.Value(0)).current;
    const inputScaleAnimations = useRef([0, 1, 2, 3, 4, 5].map(() => new Animated.Value(1))).current;

    // Get route params and set up development OTP
    const [devOtp, setDevOtp] = useState(route.params?.otp); // For development only, remove in production

    // Get auth context for login
    const { login } = useAuth();

    useEffect(() => {
        // Animate inputs sequentially on mount
        animateInputsSequentially();

        // Start the timer
        startTimer();

        // Don't auto-focus - let user tap to show keyboard when needed

        // Check notification permissions
        const checkNotifications = async () => {
            const enabled = await areNotificationsEnabled();
            setNotificationsEnabled(enabled);
        };
        checkNotifications();

        // Add keyboard listeners
        const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
            setKeyboardVisible(true);
        });
        const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
            setKeyboardVisible(false);
        });

        // No autofill permission needed - OTP will auto-fill seamlessly

        // Setup notification listener for OTP auto-fill
        const cleanupNotificationListener = setupOtpNotificationListener((receivedOtp) => {
            console.log('🎯 OTP NOTIFICATION RECEIVED:', receivedOtp);
            console.log('🎯 Starting autofill process...');

            // Convert OTP string to array
            const otpArray = receivedOtp.split('').slice(0, 6);
            while (otpArray.length < 6) {
                otpArray.push('');
            }

            console.log('🎯 OTP Array for autofill:', otpArray);

            // Always auto-fill with smooth wave animation
            animateAutoFillOtp(otpArray);
        });

        // Clean up timer, notification listener, and keyboard listeners on unmount
        return () => {
            if (timerRef.current) {
                clearInterval(timerRef.current);
            }
            cleanupNotificationListener();
            keyboardDidShowListener.remove();
            keyboardDidHideListener.remove();
        };
    }, []);

    // Listen for changes to route.params.otp
    useEffect(() => {
        if (route.params?.otp && route.params.otp !== devOtp) {
            console.log('OTP updated from route params:', route.params.otp);
            setDevOtp(route.params.otp);
        }
    }, [route.params?.otp]);

    // Animate OTP input boxes sequentially
    const animateInputsSequentially = () => {
        [0, 1, 2, 3, 4, 5].forEach((index) => {
            Animated.sequence([
                Animated.delay(index * 100),
                Animated.spring(inputScaleAnimations[index], {
                    toValue: 1.1,
                    friction: 3,
                    tension: 40,
                    useNativeDriver: true,
                }),
                Animated.spring(inputScaleAnimations[index], {
                    toValue: 1,
                    friction: 3,
                    tension: 40,
                    useNativeDriver: true,
                })
            ]).start();
        });
    };

    // Removed unused animateProgress function

    const startTimer = () => {
        setCanResend(false);
        setTimer(30);

        // Clear any existing timer
        if (timerRef.current) {
            clearInterval(timerRef.current);
        }

        // Start a new timer
        timerRef.current = setInterval(() => {
            setTimer((prevTimer) => {
                if (prevTimer <= 1) {
                    clearInterval(timerRef.current);
                    setCanResend(true);
                    return 0;
                }
                return prevTimer - 1;
            });
        }, 1000);
    };

    const handleOtpChange = (value, index) => {
        // Handle paste of full OTP (6 digits)
        if (value.length > 1) {
            if (/^\d+$/.test(value) && value.length <= 6) {
                const digits = value.split('');
                const newOtp = [...otp];

                // Fill as many inputs as we have digits, up to 6
                for (let i = 0; i < Math.min(6, digits.length); i++) {
                    newOtp[i] = digits[i];
                }

                setOtp(newOtp);

                // Focus the next empty input or the last one
                const nextIndex = Math.min(digits.length, 5);
                inputRefs.current[nextIndex]?.focus();

                // Show verify button when all digits are entered
                const otpValue = newOtp.join('');
                if (otpValue.length === 6) {
                    console.log('OTP complete, verify button will appear');
                    // Don't auto-verify, let user press verify button
                }

                return;
            }
            // If paste contains non-digits or is too long, just take the first digit
            value = value.charAt(0);
        }

        // Only allow numeric input for single character
        if (value && !/^\d+$/.test(value)) return;

        const newOtp = [...otp];
        newOtp[index] = value;
        setOtp(newOtp);

        // Move focus to next input if value is entered
        if (value && index < 5) {
            inputRefs.current[index + 1]?.focus();
        } else if (!value && index > 0) {
            inputRefs.current[index - 1]?.focus();
        }

        // Show verify button when last digit is entered
        if (value && index === 5) {
            const otpValue = newOtp.join('');
            console.log('Last digit entered, verify button will appear');
            // Don't auto-verify, let user press verify button
        }
    };

    const verifyOtp = async (otpValue) => {
        if (otpValue.length !== 6) return;

        // Check if OTP verification is blocked due to too many wrong attempts
        if (isOtpBlocked) {
            const minutes = Math.ceil(otpCooldown / 60);
            showPopupError(`Too many incorrect attempts. Please try again after ${minutes} minute${minutes > 1 ? 's' : ''}.`);
            return;
        }

        // Provide haptic feedback when submitting OTP
        Vibration.vibrate(50);

        // Animate all inputs to indicate verification in progress
        animateInputsForVerification();



        // For development, allow using the OTP from the route params
        if (devOtp && otpValue !== devOtp) {
            // Handle wrong OTP attempts
            handleWrongOtpAttempt();

            // In development mode, show a friendly error message
            showPopupError(`Incorrect OTP. Please check the OTP displayed below.`);
            setOtp(['', '', '', '', '', '']);
            // Don't auto-focus, let user tap to retry

            // Shake animation for error
            shakeInputs();
            return;
        }

        let response;
        setLoading(true);

        try {
            console.log(`Verifying OTP for phone number: ${phoneNumber}`);
            console.log(`API URL: ${API_URL}/auth/verify-otp`);
            console.log(`OTP value: ${otpValue}`);

            // Verify OTP with the backend
            response = await axios.post(`${API_URL}/auth/verify-otp`, {
                phoneNumber,
                otp: otpValue
            });

            console.log('Verify OTP response:', response.data);

            // Store the token and user data
            const { token, refreshToken, user } = response.data;

            // Handle case where refreshToken is missing
            const tokenToStore = refreshToken || token;

            console.log('Storing auth data - Token:', token);
            console.log('Storing auth data - Refresh Token:', tokenToStore);
            console.log('Storing auth data - User:', user);

            // Save token and user data using our utility function
            // If refreshToken is missing, use the access token as both tokens
            await storeAuthData(token, tokenToStore, user);

            // Update auth context
            await login(user);

            // Show success animation with enhanced haptic feedback
            setVerificationSuccess(true);

            // Provide success haptic feedback - stronger pattern for success
            Vibration.vibrate([0, 50, 50, 100, 50, 100]);

            // Animate success state with smoother spring animation
            Animated.spring(successAnimation, {
                toValue: 1,
                friction: 4,
                tension: 30,
                useNativeDriver: true,
            }).start();

            // Automatically navigate to the appropriate screen after a short delay
            setTimeout(async () => { // Make this an async function
                try {
                    // Navigate to the appropriate screen based on user type
                    console.log('Navigating based on user type:', user.userType);
                    console.log('Full user data:', user);

                    // Check for admin (case insensitive)
                    if (user.userType && (user.userType.toUpperCase() === 'ADMIN' || user.role === 'ADMIN')) {
                        console.log('Admin user detected, navigating to AdminDashboard');

                        // Force update the user type to ensure consistency
                        const updatedUser = { ...user, userType: 'ADMIN' };

                        // Update the auth context with the corrected user type
                        await login(updatedUser);

                        navigation.reset({
                            index: 0,
                            routes: [{ name: 'AdminDashboard' }],
                        });
                    }
                    // Check for delivery partner (case insensitive)
                    else if (user.userType && (user.userType.toUpperCase() === 'DELIVERY_PARTNER')) {
                        console.log('Delivery partner detected, navigating to DeliveryDashboard');

                        // Force update the user type to ensure consistency
                        const updatedUser = {
                            ...user,
                            userType: 'DELIVERY_PARTNER',
                            // Ensure these fields are properly set
                            isAvailable: user.isAvailable === undefined ? true : user.isAvailable,
                            name: user.name || 'Delivery Partner',
                            phoneNumber: user.phoneNumber || user.phone || phoneNumber
                        };

                        console.log('Updated delivery partner user data:', updatedUser);

                        // Update the auth context with the corrected user type
                        await login(updatedUser);

                        // Also directly set the delivery partner in context
                        try {
                            const { setCurrentDeliveryPartner } = require('../context/DeliveryPartnerContext').useDeliveryPartner();
                            if (setCurrentDeliveryPartner) {
                                console.log('Directly setting current delivery partner in DeliveryPartnerContext');
                                setCurrentDeliveryPartner(updatedUser);
                            }
                        } catch (contextError) {
                            console.error('Error directly setting delivery partner in context:', contextError);
                        }

                        navigation.reset({
                            index: 0,
                            routes: [{ name: 'DeliveryDashboard' }],
                        });
                    } else {
                        // Regular user - force update the user type to ensure consistency
                        const updatedUser = { ...user, userType: 'USER' };

                        // Update the auth context with the corrected user type
                        await login(updatedUser);

                        // Check if this is a new user (no name or empty name)
                        const isNewUser = !user.name || user.name.trim() === '';

                        if (isNewUser) {
                            // New user - navigate to username screen to complete profile
                            console.log('New user detected, navigating to UsernameScreen');
                            navigation.reset({
                                index: 0,
                                routes: [{
                                    name: 'UsernameScreen',
                                    params: {
                                        phoneNumber,
                                        userId: user._id,
                                        isNewUser: true
                                    }
                                }],
                            });
                        } else {
                            // Existing user - go directly to main app
                            console.log('Existing user, navigating to MainTabs');
                            navigation.reset({
                                index: 0,
                                routes: [{ name: 'MainTabs' }],
                            });
                        }
                    }
                } catch (error) {
                    console.error('Error during navigation after OTP verification:', error);
                    // Fallback to MainTabs if there's an error
                    navigation.reset({
                        index: 0,
                        routes: [{ name: 'MainTabs' }],
                    });
                }
            }, 1500); // Navigate after 1.5 seconds
        } catch (error) {
            console.error('Error verifying OTP:', error);
            let errorMessage = 'Invalid or expired OTP';

            if (error.response && error.response.data && error.response.data.message) {
                errorMessage = error.response.data.message;
            }

            // Handle wrong OTP attempts
            handleWrongOtpAttempt();

            // Provide error haptic feedback
            Vibration.vibrate([0, 50, 0, 0]);

            // Shake animation for error
            shakeInputs();

            showPopupError(errorMessage);
            setOtp(['', '', '', '', '', '']);
            inputRefs.current[0]?.focus();
        } finally {
            setLoading(false);
        }
    };

    // Handle wrong OTP attempts with progressive cooldown
    const handleWrongOtpAttempt = () => {
        const newCount = wrongOtpCount + 1;
        setWrongOtpCount(newCount);

        if (newCount >= 5) {
            // Block OTP verification for 5-10 minutes (random between 5-10 minutes)
            const cooldownMinutes = Math.floor(Math.random() * 6) + 5; // 5-10 minutes
            const cooldownSeconds = cooldownMinutes * 60;

            setOtpCooldown(cooldownSeconds);
            setIsOtpBlocked(true);

            // Start cooldown timer
            const cooldownInterval = setInterval(() => {
                setOtpCooldown(prev => {
                    if (prev <= 1) {
                        clearInterval(cooldownInterval);
                        setIsOtpBlocked(false);
                        setWrongOtpCount(0); // Reset count after cooldown
                        return 0;
                    }
                    return prev - 1;
                });
            }, 1000);

            showPopupError(`Too many incorrect attempts. Please try again after ${cooldownMinutes} minutes.`);
        } else {
            const attemptsLeft = 5 - newCount;
            showPopupError(`Incorrect OTP. ${attemptsLeft} attempt${attemptsLeft > 1 ? 's' : ''} remaining.`);
        }
    };

    // Animate inputs when verification starts
    const animateInputsForVerification = () => {
        [0, 1, 2, 3, 4, 5].forEach((index) => {
            Animated.sequence([
                Animated.timing(inputScaleAnimations[index], {
                    toValue: 0.95,
                    duration: 200,
                    useNativeDriver: true,
                }),
                Animated.timing(inputScaleAnimations[index], {
                    toValue: 1,
                    duration: 200,
                    useNativeDriver: true,
                })
            ]).start();
        });
    };

    // Animate auto-fill OTP with wave effect
    const animateAutoFillOtp = (otpArray) => {
        console.log('Starting autofill wave animation with OTP:', otpArray);

        // Clear current OTP first
        setOtp(['', '', '', '', '', '']);

        // Create wave effect - animate all inputs first, then fill sequentially
        const waveDelay = 100; // Delay between each wave animation
        const fillDelay = 200; // Delay between each digit fill

        // First, create a wave animation across all inputs
        [0, 1, 2, 3, 4, 5].forEach((index) => {
            setTimeout(() => {
                Animated.sequence([
                    Animated.timing(inputScaleAnimations[index], {
                        toValue: 1.2,
                        duration: 150,
                        useNativeDriver: true,
                    }),
                    Animated.timing(inputScaleAnimations[index], {
                        toValue: 1,
                        duration: 150,
                        useNativeDriver: true,
                    })
                ]).start();
            }, index * waveDelay);
        });

        // Then fill each digit with enhanced animation
        otpArray.forEach((digit, index) => {
            setTimeout(() => {
                console.log(`Filling digit ${index + 1}: ${digit}`);

                // Enhanced scale animation for the current input
                Animated.sequence([
                    Animated.timing(inputScaleAnimations[index], {
                        toValue: 1.3,
                        duration: 200,
                        useNativeDriver: true,
                    }),
                    Animated.timing(inputScaleAnimations[index], {
                        toValue: 1,
                        duration: 200,
                        useNativeDriver: true,
                    })
                ]).start();

                // Update the OTP state with the new digit
                setOtp(prevOtp => {
                    const newOtp = [...prevOtp];
                    newOtp[index] = digit;
                    console.log(`Updated OTP state:`, newOtp);
                    return newOtp;
                });

                // Add a subtle vibration for the first digit
                if (index === 0) {
                    Vibration.vibrate(30);
                }

                // After the last digit is filled, show verify button
                if (index === otpArray.length - 1) {
                    setTimeout(() => {
                        console.log('Autofill complete, ready for user verification');
                        // Don't auto-verify, let user press verify button
                    }, 400); // Wait for animation to complete
                }
            }, (index * fillDelay) + 600); // Start after wave animation
        });
    };

    // Shake animation for error
    const shakeInputs = () => {
        // Create a sequence of small movements to create a shake effect
        Animated.sequence([
            Animated.timing(fadeAnim, { toValue: 0.5, duration: 50, useNativeDriver: true }),
            Animated.timing(fadeAnim, { toValue: 1, duration: 50, useNativeDriver: true }),
        ]).start();

        [0, 1, 2, 3, 4, 5].forEach((index) => {
            Animated.sequence([
                Animated.timing(inputScaleAnimations[index], {
                    toValue: 1.1,
                    duration: 50,
                    useNativeDriver: true,
                }),
                Animated.timing(inputScaleAnimations[index], {
                    toValue: 0.9,
                    duration: 50,
                    useNativeDriver: true,
                }),
                Animated.timing(inputScaleAnimations[index], {
                    toValue: 1.1,
                    duration: 50,
                    useNativeDriver: true,
                }),
                Animated.timing(inputScaleAnimations[index], {
                    toValue: 0.9,
                    duration: 50,
                    useNativeDriver: true,
                }),
                Animated.timing(inputScaleAnimations[index], {
                    toValue: 1,
                    duration: 50,
                    useNativeDriver: true,
                }),
            ]).start();
        });
    };

    const handleKeyPress = (e, index) => {
        // Handle backspace key to move focus to previous input
        if (e.nativeEvent.key === 'Backspace') {
            if (!otp[index] && index > 0) {
                // If current input is empty, move focus to previous input
                inputRefs.current[index - 1]?.focus();
            } else {
                // If current input has value, clear it but keep focus
                const newOtp = [...otp];
                newOtp[index] = '';
                setOtp(newOtp);
            }
        }
    };

    const handleResendOTP = async () => {
        if (!canResend || resendCooldown > 0) return;

        // Check resend limit (max 5 attempts)
        if (resendCount >= 5) {
            showPopupError('Maximum resend attempts reached. Please try again later.');
            return;
        }

        setLoading(true);
        setResendCount(prev => prev + 1);

        // Provide haptic feedback when resending OTP
        Vibration.vibrate(50);

        // Reset the timer
        startTimer();

        // Set cooldown period (increases with each resend)
        const cooldownTime = Math.min(60, 30 + (resendCount * 10)); // 30s, 40s, 50s, 60s max
        setResendCooldown(cooldownTime);

        const cooldownInterval = setInterval(() => {
            setResendCooldown(prev => {
                if (prev <= 1) {
                    clearInterval(cooldownInterval);
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);

        try {
            // Resend OTP to the user
            const response = await axios.post(`${API_URL}/auth/send-otp`, {
                phoneNumber
            });

            console.log('Resend OTP response:', response.data);

            // Check if push notification was sent
            const pushSent = response.data.pushNotificationSent;

            // For development only, update the devOtp state with the new OTP
            if (response.data.otp) {
                // Get the new OTP from the response
                const newOtp = response.data.otp;

                // Update the route params with the new OTP
                if (route.params) {
                    route.params.otp = newOtp;
                }

                // Update the local devOtp state variable
                setDevOtp(newOtp);

                // Set the new OTP in the navigation params as well
                navigation.setParams({ otp: newOtp });

                // Show appropriate success message
                if (pushSent) {
                    showPopupSuccess('OTP sent via notification');
                } else if (process.env.NODE_ENV === 'development') {
                    showPopupSuccess(`New OTP: ${newOtp}`);
                } else {
                    showPopupSuccess('OTP has been resent');
                }

                // Log the new OTP for debugging
                console.log('New OTP set:', newOtp);
            } else {
                const message = pushSent ? 'OTP sent via notification' : 'OTP has been resent';
                showPopupSuccess(message);
            }

            // Clear the current OTP input fields
            setOtp(['', '', '', '', '', '']);
            inputRefs.current[0]?.focus();

            // Animate the OTP input fields to indicate a fresh start
            animateInputsSequentially();

        } catch (error) {
            console.error('Error resending OTP:', error);
            let errorMessage = 'Failed to resend OTP';

            if (error.response && error.response.data && error.response.data.message) {
                errorMessage = error.response.data.message;
            }

            showPopupError(errorMessage);
            setResendCount(prev => Math.max(0, prev - 1)); // Revert count on error
        } finally {
            setLoading(false);
        }
    };

    const showPopupSuccess = (message) => {
        setAlertMessage(message);
        setAlertType('success');
        animatePopup();
    };

    const showPopupError = (message) => {
        setAlertMessage(message);
        setAlertType('error');
        animatePopup();
    };

    const animatePopup = () => {
        setShowPopup(true);
        Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
        }).start(() => {
            setTimeout(() => {
                Animated.timing(fadeAnim, {
                    toValue: 0,
                    duration: 300,
                    useNativeDriver: true,
                }).start(() => setShowPopup(false));
            }, 2000);
        });
    };

    // Auto-focus first input for better UX
    const focusFirstInput = () => {
        setTimeout(() => {
            inputRefs.current[0]?.focus();
        }, 500);
    };

    return (
        <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} className="flex-1 bg-white">
            <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />

            {/* Unique asymmetric design elements */}
            <View className="absolute top-0 right-0 w-[70%] h-[220px] overflow-hidden">
                <View className="absolute w-[300px] h-[300px] rounded-full bg-madder opacity-[0.08] top-[-100px] right-[-50px]" />
            </View>

            <View className="absolute bottom-0 left-0 w-[50%] h-[200px] overflow-hidden">
                <View className="absolute w-[250px] h-[250px] rounded-full bg-madder opacity-[0.05] bottom-[-100px] left-[-50px]" />
            </View>

            {/* Unique back button */}
            <TouchableOpacity
                className="absolute top-5 left-5 z-10 w-10 h-10 rounded-lg bg-gray-100 items-center justify-center shadow-sm"
                style={{
                    elevation: 2,
                    shadowColor: '#000',
                    shadowOffset: { width: 0, height: 1 },
                    shadowOpacity: 0.1,
                    shadowRadius: 2,
                }}
                onPress={() => navigation.goBack()}
            >
                <MaterialIcons name="keyboard-arrow-left" size={28} color="#333" />
            </TouchableOpacity>

            <View className="flex-1 px-6">
                {/* Unique header section */}
                <View className="mt-20 mb-10">
                    {/* Logo with unique presentation */}
                    <View className="flex-row items-center mb-8">
                        <View className="w-[60px] h-[60px] rounded-xl bg-white items-center justify-center shadow-md mr-4"
                            style={{
                                transform: [{ rotate: '-5deg' }],
                                elevation: 4,
                                shadowColor: '#000',
                                shadowOffset: { width: 0, height: 2 },
                                shadowOpacity: 0.1,
                                shadowRadius: 4,
                            }}>
                            <Image
                                source={require('../assets/logo.png')}
                                className="w-[45px] h-[45px]"
                                resizeMode="contain"
                            />
                        </View>

                        <View>
                            <Text className="text-2xl font-bold text-gray-800 tracking-wide">
                                Verification
                            </Text>
                            <Text className="text-sm text-gray-600 mt-1">
                                OTP sent to {phoneNumber}
                            </Text>
                        </View>
                    </View>

                    {/* Dynamic instruction based on notification status */}
                    <Text className="text-base text-gray-600 leading-6 mb-3">
                        {pushNotificationSent
                            ? "Check your notifications for the OTP code"
                            : "Please enter the verification code we sent to your phone"
                        }
                    </Text>

                    {/* Notification status indicator */}
                    {hasPushToken && (
                        <View className={`flex-row items-center p-2 rounded-lg mb-3 ${
                            pushNotificationSent ? 'bg-green-50' : 'bg-orange-50'
                        }`}>
                            <MaterialIcons
                                name={pushNotificationSent ? 'notifications-active' : 'notifications-off'}
                                size={16}
                                color={pushNotificationSent ? '#16A34A' : '#F59E0B'}
                            />
                            <Text className={`ml-2 text-sm ${
                                pushNotificationSent ? 'text-green-700' : 'text-orange-700'
                            }`}>
                                {pushNotificationSent
                                    ? 'OTP sent via notification'
                                    : 'Notifications available for faster OTP'
                                }
                            </Text>
                        </View>
                    )}

                    {!notificationsEnabled && !hasPushToken && (
                        <View className="flex-row items-center p-2 rounded-lg mb-3 bg-blue-50">
                            <MaterialIcons name="info" size={16} color="#3B82F6" />
                            <Text className="ml-2 text-sm text-blue-700">
                                Enable notifications for instant OTP delivery
                            </Text>
                        </View>
                    )}

                    {/* OTP Auto-fill is always enabled */}
                </View>

                {/* Enhanced OTP input design */}
                <View className="mb-10">
                    {/* OTP Input with clean underline styling */}
                    <View className="flex-row justify-between mb-8 px-2">
                        {[0, 1, 2, 3, 4, 5].map((index) => (
                            <Animated.View
                                key={index}
                                style={{
                                    width: '14%',
                                    transform: [{ scale: inputScaleAnimations[index] }]
                                }}
                            >
                                <TextInput
                                    ref={(ref) => (inputRefs.current[index] = ref)}
                                    className="h-16 w-full text-center text-3xl font-bold text-gray-800 bg-transparent"
                                    style={{
                                        borderWidth: 0,
                                        borderBottomWidth: 3,
                                        borderBottomColor: otp[index] ? '#A31621' : '#E5E5E5',
                                        backgroundColor: 'transparent',
                                    }}
                                    maxLength={1}
                                    keyboardType="number-pad"
                                    value={otp[index]}
                                    onChangeText={(value) => handleOtpChange(value, index)}
                                    onKeyPress={(e) => handleKeyPress(e, index)}
                                    onFocus={() => {
                                        // When user taps on input, ensure keyboard shows
                                        setKeyboardVisible(true);
                                    }}
                                    caretHidden={false}
                                    selectTextOnFocus={true}
                                    editable={!isOtpBlocked}
                                    showSoftInputOnFocus={!isOtpBlocked}
                                />
                            </Animated.View>
                        ))}
                    </View>



                    {/* Display OTP for development */}
                    {devOtp && (
                        <View className="mt-5 p-3 bg-gray-50 rounded-lg border-l-4 border-l-madder">
                            <View className="flex-row items-center">
                                <Ionicons name="code-slash-outline" size={16} color="#A31621" />
                                <Text className="ml-2 text-gray-600" style={{
                                    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
                                }}>
                                    Dev OTP: <Text className="font-bold text-madder">{devOtp}</Text>
                                </Text>
                            </View>
                        </View>
                    )}



                    {/* Wrong attempts indicator */}
                    {wrongOtpCount > 0 && !isOtpBlocked && (
                        <View className="mt-4 p-3 bg-madder/5 rounded-xl border-l-4 border-l-madder shadow-sm"
                            style={{
                                elevation: 1,
                                shadowColor: '#000',
                                shadowOffset: { width: 0, height: 1 },
                                shadowOpacity: 0.05,
                                shadowRadius: 1,
                            }}>
                            <View className="flex-row items-center">
                                <MaterialIcons name="warning" size={18} color="#A31621" />
                                <Text className="ml-3 text-madder text-sm font-medium">
                                    {wrongOtpCount} incorrect attempt{wrongOtpCount > 1 ? 's' : ''}. {5 - wrongOtpCount} remaining.
                                </Text>
                            </View>
                        </View>
                    )}

                    {/* OTP blocked indicator */}
                    {isOtpBlocked && (
                        <View className="mt-4 p-4 bg-madder/10 rounded-xl border-l-4 border-l-madder shadow-sm"
                            style={{
                                elevation: 2,
                                shadowColor: '#000',
                                shadowOffset: { width: 0, height: 1 },
                                shadowOpacity: 0.1,
                                shadowRadius: 2,
                            }}>
                            <View className="flex-row items-center mb-2">
                                <MaterialIcons name="block" size={20} color="#A31621" />
                                <Text className="ml-3 text-madder font-bold text-sm">
                                    OTP Verification Blocked
                                </Text>
                            </View>
                            <Text className="text-madder/80 text-sm leading-5">
                                Too many incorrect attempts. Please wait {Math.ceil(otpCooldown / 60)} minute{Math.ceil(otpCooldown / 60) > 1 ? 's' : ''} before trying again.
                            </Text>
                        </View>
                    )}

                    {/* Manual Verify Button - Show when OTP is complete but not auto-verifying */}
                    {otp.join('').length === 6 && !loading && !verificationSuccess && !isOtpBlocked && (
                        <View className="items-center mt-6">
                            <TouchableOpacity
                                onPress={() => verifyOtp(otp.join(''))}
                                className="bg-madder py-3 px-8 rounded-lg shadow-md"
                                style={{
                                    elevation: 3,
                                    shadowColor: '#000',
                                    shadowOffset: { width: 0, height: 2 },
                                    shadowOpacity: 0.1,
                                    shadowRadius: 3,
                                }}
                            >
                                <Text className="text-white font-semibold text-base">
                                    Verify OTP
                                </Text>
                            </TouchableOpacity>
                        </View>
                    )}
                </View>

                {/* Loading Indicator with unique styling */}
                {loading && (
                    <View className="items-center mt-5">
                        <View className="w-[50px] h-[50px] rounded-full bg-madder/10 items-center justify-center">
                            <ActivityIndicator size="small" color="#A31621" />
                        </View>
                        <Text className="text-madder mt-3 font-medium">
                            Verifying...
                        </Text>
                    </View>
                )}

                {/* Success animation with enhanced design */}
                {verificationSuccess && (
                    <View style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        backgroundColor: 'white',
                        alignItems: 'center',
                        justifyContent: 'center',
                        zIndex: 9999,
                    }}>
                        <Animated.View style={{
                            width: '100%',
                            alignItems: 'center',
                            justifyContent: 'center',
                            transform: [{ scale: successAnimation }],
                            opacity: successAnimation
                        }}>
                            <View className="w-[120px] h-[120px] rounded-full bg-green-100 items-center justify-center mb-6 shadow-lg"
                                style={{
                                    elevation: 8,
                                    shadowColor: '#000',
                                    shadowOffset: { width: 0, height: 4 },
                                    shadowOpacity: 0.15,
                                    shadowRadius: 8,
                                }}>
                                <AntDesign name="check" size={70} color="#16A34A" />
                            </View>

                            <Text className="text-green-600 text-3xl font-bold mb-4 text-center">
                                Verification Successful
                            </Text>

                            <Text className="text-gray-600 text-base text-center px-10">
                                Your phone number has been verified successfully.
                            </Text>
                        </Animated.View>
                    </View>
                )}

                {/* Alert Message with unique styling */}
                {showPopup && (
                    <Animated.View
                        className={`mt-5 p-3 rounded-lg border-l-4 ${
                            alertType === 'success' ? 'bg-green-50 border-l-green-500' : 'bg-red-50 border-l-red-500'
                        }`}
                        style={{
                            opacity: fadeAnim,
                            transform: [{
                                translateY: fadeAnim.interpolate({
                                    inputRange: [0, 1],
                                    outputRange: [10, 0]
                                })
                            }]
                        }}
                    >
                        <View className="flex-row items-center">
                            <MaterialIcons
                                name={alertType === 'success' ? 'check-circle' : 'error-outline'}
                                size={18}
                                color={alertType === 'success' ? "#16A34A" : "#DC2626"}
                            />
                            <Text className={`ml-2 font-medium ${
                                alertType === 'success' ? 'text-green-700' : 'text-red-700'
                            }`}>
                                {alertMessage}
                            </Text>
                        </View>
                    </Animated.View>
                )}

                {/* Resend OTP section with enhanced styling */}
                <View className="items-center mt-auto mb-10">
                    <Text className="text-gray-600 text-sm mb-3">
                        Didn't receive the code?
                    </Text>

                    {/* Show attempt count if user has tried resending */}
                    {resendCount > 0 && (
                        <Text className="text-gray-500 text-xs mb-2">
                            Attempts: {resendCount}/5
                        </Text>
                    )}

                    {!canResend || resendCooldown > 0 ? (
                        <View className="flex-row items-center bg-gray-100 py-3 px-6 rounded-lg">
                            <Text className="text-gray-500 text-sm">
                                {resendCooldown > 0
                                    ? `Wait ${resendCooldown}s before resending`
                                    : `Resend in ${timer}s`
                                }
                            </Text>
                        </View>
                    ) : resendCount >= 5 ? (
                        <View className="flex-row items-center bg-red-100 py-3 px-6 rounded-lg">
                            <MaterialIcons name="block" size={16} color="#DC2626" />
                            <Text className="text-red-600 text-sm ml-2">
                                Maximum attempts reached
                            </Text>
                        </View>
                    ) : (
                        <TouchableOpacity
                            onPress={handleResendOTP}
                            className="py-3 px-6 bg-madder/10 rounded-lg"
                            disabled={loading}
                        >
                            <Text className="text-madder font-semibold">
                                {pushNotificationSent ? 'Resend Code' : 'Resend Code'}
                            </Text>
                        </TouchableOpacity>
                    )}

                    {/* Troubleshooting help */}
                    {resendCount >= 2 && (
                        <View className="mt-4 p-3 bg-blue-50 rounded-lg">
                            <Text className="text-blue-700 text-xs text-center">
                                💡 Tip: Check your notification settings if you're not receiving OTP notifications
                            </Text>
                        </View>
                    )}
                </View>
            </View>

            {/* OTP Auto-fill works seamlessly without permission dialogs */}
        </KeyboardAvoidingView>
    );
};

export default OTPScreen;
