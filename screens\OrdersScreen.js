import React, { useEffect, useRef, useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image, Alert, Modal, Animated, ActivityIndicator, Linking, Easing, BackHandler, RefreshControl } from 'react-native';
import { MaterialIcons } from "@expo/vector-icons";
import { useOrders } from '../context/OrderContext';
import { format } from 'date-fns';
import { useFocusEffect } from '@react-navigation/native';
import * as Notifications from 'expo-notifications';

const ORDER_STATUS = {
    PLACED: {
        label: 'Order Placed',
        icon: 'receipt-long',
        color: '#EF4444',
        bgColor: 'bg-red-100',
        textColor: 'text-red-600',
        description: 'Your order has been received'
    },
    CONFIRMED: {
        label: 'Order Confirmed',
        icon: 'check-circle',
        color: '#10B981',
        bgColor: 'bg-green-100',
        textColor: 'text-green-600',
        description: 'Your order is confirmed and being processed'
    },
    PREPARING: {
        label: 'Preparing Order',
        icon: 'restaurant',
        color: '#F59E0B',
        bgColor: 'bg-amber-100',
        textColor: 'text-amber-600',
        description: 'Your order is being prepared'
    },
    OUT_FOR_DELIVERY: {
        label: 'Out for Delivery',
        icon: 'delivery-dining',
        color: '#6366F1',
        bgColor: 'bg-indigo-100',
        textColor: 'text-indigo-600',
        description: 'Your order is on the way'
    },
    DELIVERED: {
        label: 'Delivered',
        icon: 'done-all',
        color: '#059669',
        bgColor: 'bg-emerald-100',
        textColor: 'text-emerald-600',
        description: 'Your order has been delivered'
    },
    CANCELLED: {
        label: 'Cancelled',
        icon: 'cancel',
        color: '#6B7280',
        bgColor: 'bg-gray-100',
        textColor: 'text-gray-600',
        description: 'Your order has been cancelled'
    }
};

const OrdersScreen = ({ route, navigation }) => {
    const { orders, cancelOrder, canCancelOrder, fetchUserOrders, loading } = useOrders();
    const scrollViewRef = useRef(null);
    const newOrderId = route.params?.newOrderId;
    const [selectedOrder, setSelectedOrder] = useState(null);
    const [selectedOrderData, setSelectedOrderData] = useState(null);
    const [detailModalVisible, setDetailModalVisible] = useState(false);

    // New state variables for cancel modal
    const [cancelModalVisible, setCancelModalVisible] = useState(false);
    const [orderToCancel, setOrderToCancel] = useState(null);

    // Add the missing state variables for success modal
    const [cancelSuccessModalVisible, setCancelSuccessModalVisible] = useState(false);
    const [cancelledOrderDetails, setCancelledOrderDetails] = useState(null);

    // State for tracking expanded delivery partner info
    const [expandedDeliveryInfo, setExpandedDeliveryInfo] = useState(false);

    // State for managing different loading states
    const [isInitialLoading, setIsInitialLoading] = useState(true);
    const [isRefreshing, setIsRefreshing] = useState(false);
    const [lastFetchTime, setLastFetchTime] = useState(0);

    // Optimized fetch function with loading states
    const fetchOrdersOptimized = async (isBackground = false) => {
        const now = Date.now();
        // Prevent too frequent requests (minimum 5 seconds between requests)
        if (now - lastFetchTime < 5000 && !isBackground) {
            return;
        }

        try {
            if (!isBackground) {
                setIsRefreshing(true);
            }

            await fetchUserOrders();
            setLastFetchTime(now);

            if (isInitialLoading) {
                setIsInitialLoading(false);
            }
        } catch (error) {
            console.error('Error fetching orders:', error);
        } finally {
            if (!isBackground) {
                setIsRefreshing(false);
            }
        }
    };

    // Initial fetch when component mounts
    useEffect(() => {
        fetchOrdersOptimized();
    }, []);

    // Smart background polling - only for active orders
    useEffect(() => {
        let interval;

        const hasActiveOrders = orders.some(order =>
            order.status !== 'DELIVERED' && order.status !== 'CANCELLED'
        );

        if (hasActiveOrders && orders.length > 0) {
            interval = setInterval(() => {
                fetchOrdersOptimized(true); // Background fetch
            }, 60000); // Increased to 60 seconds for better performance
        }

        return () => {
            if (interval) {
                clearInterval(interval);
            }
        };
    }, [orders]);

    // Refresh only when screen comes into focus (with debouncing)
    useFocusEffect(
        React.useCallback(() => {
            const now = Date.now();
            // Only refresh if it's been more than 10 seconds since last fetch
            if (now - lastFetchTime > 10000) {
                fetchOrdersOptimized();
            }
        }, [lastFetchTime])
    );

    // Optimized notification listeners with debouncing
    useEffect(() => {
        const notificationListener = Notifications.addNotificationReceivedListener(notification => {
            console.log('Notification received in OrdersScreen:', notification);

            // Check if it's an order status update notification
            const { data } = notification.request.content;
            if (data?.type === 'order_status_update') {
                console.log('Order status update notification received, refreshing orders...');
                // Use optimized fetch with debouncing
                fetchOrdersOptimized();
            }
        });

        const responseListener = Notifications.addNotificationResponseReceivedListener(response => {
            console.log('Notification response received in OrdersScreen:', response);

            // Check if it's an order status update notification
            const { data } = response.notification.request.content;
            if (data?.type === 'order_status_update') {
                console.log('Order status update notification tapped, refreshing orders...');
                // Use optimized fetch with debouncing
                fetchOrdersOptimized();
            }
        });

        return () => {
            notificationListener.remove();
            responseListener.remove();
        };
    }, []);

    // Handle hardware back button to navigate to home screen
    useFocusEffect(
        React.useCallback(() => {
            const onBackPress = () => {
                // Navigate to home screen instead of going back to checkout
                navigation.navigate('MainTabs', { screen: 'Home' });
                return true; // Prevent default back behavior
            };

            const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);

            return () => subscription?.remove();
        }, [navigation])
    );

    // Enhanced animation values
    const scaleAnim = useRef(new Animated.Value(0.5)).current;
    const opacityAnim = useRef(new Animated.Value(0)).current;
    const bgOpacityAnim = useRef(new Animated.Value(0)).current;
    const iconScaleAnim = useRef(new Animated.Value(0.5)).current;
    const buttonScaleAnim = useRef(new Animated.Value(0.95)).current;

    // Enhanced animation functions for smoother transitions
    const animateIn = () => {
        // Reset animation values
        scaleAnim.setValue(0.5);
        opacityAnim.setValue(0);
        bgOpacityAnim.setValue(0);
        iconScaleAnim.setValue(0.5);

        Animated.sequence([
            // First fade in the background
            Animated.timing(bgOpacityAnim, {
                toValue: 1,
                duration: 200,
                useNativeDriver: true,
                easing: Easing.out(Easing.cubic),
            }),
            // Then animate the modal with a nice spring effect
            Animated.parallel([
                Animated.spring(scaleAnim, {
                    toValue: 1,
                    friction: 6.5,
                    tension: 50,
                    useNativeDriver: true,
                }),
                Animated.timing(opacityAnim, {
                    toValue: 1,
                    duration: 250,
                    useNativeDriver: true,
                    easing: Easing.out(Easing.cubic),
                }),
                // Animate the icon with a slight delay and bounce
                Animated.sequence([
                    Animated.delay(150),
                    Animated.spring(iconScaleAnim, {
                        toValue: 1.2,
                        friction: 5,
                        tension: 40,
                        useNativeDriver: true,
                    }),
                    Animated.spring(iconScaleAnim, {
                        toValue: 1,
                        friction: 5,
                        tension: 40,
                        useNativeDriver: true,
                    }),
                ]),
                // Animate the button
                Animated.spring(buttonScaleAnim, {
                    toValue: 1,
                    friction: 6,
                    tension: 40,
                    useNativeDriver: true,
                }),
            ]),
        ]).start();
    };

    const animateOut = (callback) => {
        Animated.parallel([
            // Animate the modal out
            Animated.timing(scaleAnim, {
                toValue: 0.8,
                duration: 150,
                useNativeDriver: true,
                easing: Easing.in(Easing.cubic),
            }),
            Animated.timing(opacityAnim, {
                toValue: 0,
                duration: 200,
                useNativeDriver: true,
                easing: Easing.in(Easing.cubic),
            }),
            // Fade out the background
            Animated.timing(bgOpacityAnim, {
                toValue: 0,
                duration: 250,
                useNativeDriver: true,
                easing: Easing.in(Easing.cubic),
            }),
        ]).start(callback);
    };

    // Effect to trigger animation when modal opens
    useEffect(() => {
        if (cancelModalVisible || cancelSuccessModalVisible || detailModalVisible) {
            animateIn();
        }
    }, [cancelModalVisible, cancelSuccessModalVisible, detailModalVisible]);

    // Effect to handle selected order
    useEffect(() => {
        if (selectedOrder) {
            // Find the order by id or _id, with string conversion for reliable comparison
            const orderData = orders.find(order =>
                (order.id && order.id.toString() === selectedOrder.toString()) ||
                (order._id && order._id.toString() === selectedOrder.toString())
            );

            if (orderData) {
                setSelectedOrderData(orderData);
                setDetailModalVisible(true);
            }
        } else {
            setSelectedOrderData(null);
        }
    }, [selectedOrder, orders]);

    // Memoized order separation for performance
    const currentOrders = React.useMemo(() =>
        orders.filter(order =>
            order.status !== 'DELIVERED' && order.status !== 'CANCELLED'
        ), [orders]
    );

    const pastOrders = React.useMemo(() =>
        orders.filter(order =>
            order.status === 'DELIVERED' || order.status === 'CANCELLED'
        ), [orders]
    );

    useEffect(() => {
        if (newOrderId && scrollViewRef.current) {
            // Scroll to top when new order is added
            scrollViewRef.current.scrollTo({ y: 0, animated: true });
        }
    }, [newOrderId]);

    const handleCancelOrder = (order) => {
        // Calculate the final price (with discounts applied) - same logic as in renderOrderCard
        const originalPrice = order.originalAmount || order.total || 0;
        const couponDiscount = order.couponDiscount || 0;
        const coinsDiscount = order.coinsDiscount || 0;
        const finalPrice = order.totalAmount || (originalPrice - couponDiscount - coinsDiscount);

        // Create a copy of the order with the calculated final price
        const orderWithFinalPrice = {
            ...order,
            calculatedTotal: finalPrice
        };

        setOrderToCancel(orderWithFinalPrice);
        setCancelModalVisible(true);
    };

    const confirmCancelOrder = async () => {
        if (orderToCancel && (orderToCancel._id || orderToCancel.id)) {
            try {
                const orderDetails = {
                    id: orderToCancel._id || orderToCancel.id,
                    orderNumber: orderToCancel.orderNumber || orderToCancel._id || orderToCancel.id,
                    total: orderToCancel.calculatedTotal || orderToCancel.totalAmount || orderToCancel.total || 0
                };

                animateOut(() => {
                    setCancelModalVisible(false);
                    setTimeout(async () => {
                        // Call the API to cancel the order
                        const success = await cancelOrder(orderToCancel._id || orderToCancel.id);

                        if (success) {
                            setCancelledOrderDetails(orderDetails);
                            setCancelSuccessModalVisible(true);
                        } else {
                            Alert.alert(
                                "Error",
                                "Failed to cancel order. Please try again."
                            );
                        }

                        setOrderToCancel(null);
                    }, 300);
                });
            } catch (error) {
                console.error('Error cancelling order:', error);
                Alert.alert(
                    "Error",
                    "An error occurred while cancelling your order. Please try again."
                );
                setCancelModalVisible(false);
            }
        }
    };

    // New function to render compact past orders
    const renderCompactPastOrder = (order) => {
        // Format the order date if it's valid
        const orderDate = isValidDate(order.date)
            ? format(new Date(order.date), 'dd MMM yyyy')
            : "Invalid Date";

        // Format the order time if it's valid
        const orderTime = isValidDate(order.date)
            ? format(new Date(order.date), 'hh:mm a')
            : "";

        // Calculate the final price (with discounts applied)
        const originalPrice = order.originalAmount || order.total || 0;
        const couponDiscount = order.couponDiscount || 0;
        const coinsDiscount = order.coinsDiscount || 0;
        const finalPrice = order.totalAmount || (originalPrice - couponDiscount - coinsDiscount);

        // Get delivery date if available
        const deliveryDate = order.deliveredAt ?
            format(new Date(order.deliveredAt), 'dd MMM yyyy') :
            (order.status === 'DELIVERED' ? orderDate : null);

        // Get delivery time if available
        const deliveryTime = order.deliveredAt ?
            format(new Date(order.deliveredAt), 'hh:mm a') :
            (order.status === 'DELIVERED' ? orderTime : null);

        return (
            <View key={order.id || order._id || `order-${Math.random()}`} className="bg-white rounded-xl shadow-sm mb-3 overflow-hidden border border-gray-100">
                {/* Compact Header */}
                <View className="p-3 border-b border-gray-100">
                    <View className="flex-row justify-between items-center">
                        <View className="flex-row items-center flex-1">
                            <View className={`w-8 h-8 rounded-full items-center justify-center ${
                                order.status === 'DELIVERED' ? 'bg-green-100' : 'bg-gray-100'
                            }`}>
                                <MaterialIcons
                                    name={order.status === 'DELIVERED' ? 'check-circle' : 'cancel'}
                                    size={16}
                                    color={order.status === 'DELIVERED' ? '#10B981' : '#6B7280'}
                                />
                            </View>
                            <View className="ml-2">
                                <Text className="font-medium text-sm text-gray-800">
                                    Order #{order.orderNumber || order.id || order._id}
                                </Text>
                                <Text className="text-gray-500 text-xs">
                                    {orderDate} • {orderTime}
                                </Text>
                            </View>
                        </View>
                        <View className="bg-white border border-gray-100 rounded-lg p-1.5 shadow-sm">
                            <Text className="text-madder font-bold text-sm">₹{finalPrice}</Text>
                        </View>
                    </View>
                </View>

                {/* Compact Content */}
                <View className="p-3">
                    <View className="flex-row justify-between items-start">
                        {/* Order Items Summary */}
                        <View className="flex-1 mr-3">
                            <Text className="font-medium text-xs text-gray-700 mb-1">Items:</Text>
                            <Text className="text-gray-600 text-xs">
                                {order.items && order.items.length > 0
                                    ? (order.items.map(item => item?.name || 'Unknown Item').join(', ').length > 40
                                        ? order.items.map(item => item?.name || 'Unknown Item').join(', ').substring(0, 40) + '...'
                                        : order.items.map(item => item?.name || 'Unknown Item').join(', '))
                                    : 'No items'}
                            </Text>

                            {/* Payment Mode Only (No Status Badge) */}
                            <View className="mt-2 flex-row items-center">
                                {(order.paymentMode || order.paymentMethod) && (
                                    <View className="flex-row items-center">
                                        <MaterialIcons
                                            name={['COD', 'cod'].includes(order.paymentMode || order.paymentMethod) ? 'payments' : 'account-balance'}
                                            size={12}
                                            color="#10B981"
                                        />
                                        <Text className="text-gray-600 text-xs ml-1">
                                            {(order.paymentMode || order.paymentMethod) === 'COD' ? 'COD' :
                                             (order.paymentMode || order.paymentMethod) === 'cod' ? 'COD' :
                                             (order.paymentMode || order.paymentMethod) === 'UPI' ? 'UPI' :
                                             (order.paymentMode || order.paymentMethod) === 'upi' ? 'UPI' :
                                             (order.paymentMode || order.paymentMethod)}
                                        </Text>
                                    </View>
                                )}
                            </View>
                        </View>

                        {/* Delivery Info */}
                        <View className="items-end">
                            {order.status === 'DELIVERED' && (
                                <>
                                    <Text className="font-medium text-xs text-gray-700">Delivered on:</Text>
                                    <Text className="text-green-700 text-xs">{deliveryDate}</Text>
                                    <Text className="text-green-700 text-xs">{deliveryTime}</Text>
                                </>
                            )}
                            {order.status === 'CANCELLED' && (
                                <Text className="text-gray-500 text-xs">Order was cancelled</Text>
                            )}
                        </View>
                    </View>

                    {/* Delivery Partner - If Available */}
                    {order.deliveryPartner && order.status === 'DELIVERED' && (
                        <View className="mt-2 pt-2 border-t border-gray-100">
                            <View className="flex-row items-center">
                                <MaterialIcons name="person" size={14} color="#6366F1" />
                                <Text className="text-gray-700 text-xs ml-1">Delivered by:</Text>
                                <Text className="text-indigo-700 text-xs ml-1 font-medium">{order.deliveryPartner?.name || 'Unknown'}</Text>
                            </View>
                        </View>
                    )}
                </View>

                {/* View Details Button */}
                <TouchableOpacity
                    className="bg-gray-50 p-2 border-t border-gray-100"
                    onPress={() => {
                        // Use either id or _id depending on what's available
                        const orderId = order.id || order._id;
                        setSelectedOrder(orderId);
                    }}
                >
                    <Text className="text-center text-madder text-xs font-medium">View Details</Text>
                </TouchableOpacity>
            </View>
        );
    };

    const renderOrderStatus = (currentStatus, order) => {
        // Handle cancelled orders separately
        if (currentStatus === 'CANCELLED') {
            return (
                <View className="px-4 py-2">
                    <View style={{
                        backgroundColor: 'white',
                        borderRadius: 12,
                        padding: 12,
                        marginBottom: 16,
                        shadowColor: '#000',
                        shadowOffset: { width: 0, height: 1 },
                        shadowOpacity: 0.1,
                        shadowRadius: 2,
                        elevation: 2,
                        borderWidth: 1,
                        borderColor: '#F3F4F6'
                    }}>
                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                            <View style={{
                                width: 40,
                                height: 40,
                                borderRadius: 20,
                                alignItems: 'center',
                                justifyContent: 'center',
                                backgroundColor: '#6B7280',
                                marginRight: 12
                            }}>
                                <MaterialIcons name="cancel" size={22} color="white" />
                            </View>
                            <View style={{ flex: 1 }}>
                                <Text style={{ fontWeight: 'bold', fontSize: 14, color: '#1F2937' }}>
                                    Order Cancelled
                                </Text>
                                <Text style={{ color: '#4B5563', fontSize: 12, marginTop: 2 }}>
                                    This order has been cancelled
                                </Text>
                                {order.cancelledAt && (
                                    <View style={{ marginTop: 4, flexDirection: 'row', alignItems: 'center' }}>
                                        <MaterialIcons name="schedule" size={12} color="#6B7280" />
                                        <Text style={{ fontSize: 10, color: '#6B7280', marginLeft: 4 }}>
                                            {new Date(order.cancelledAt).toLocaleDateString("en-IN", { day: "numeric", month: "short" })} at {" "}
                                            {new Date(order.cancelledAt).toLocaleTimeString("en-IN", { hour: '2-digit', minute: '2-digit', hour12: true })}
                                        </Text>
                                    </View>
                                )}
                            </View>
                        </View>
                    </View>
                </View>
            );
        }

        // Simplified status order with only 3 key statuses
        const statusOrder = ['PLACED', 'OUT_FOR_DELIVERY', 'DELIVERED'];
        // Map current status to one of the three key statuses
        let mappedStatus = currentStatus;
        if (currentStatus === 'CONFIRMED' || currentStatus === 'PREPARING') {
            mappedStatus = 'PLACED'; // Map intermediate statuses to PLACED
        }
        const currentIndex = statusOrder.indexOf(mappedStatus);

        // Timeline rendering logic follows

        return (
            <View className="px-4 py-2">
                {/* Current Status Card - Simplified UI */}
                {currentStatus !== 'DELIVERED' && currentStatus !== 'CANCELLED' && (
                    <View style={{
                        backgroundColor: 'white',
                        borderRadius: 12,
                        padding: 12,
                        marginBottom: 16,
                        shadowColor: '#000',
                        shadowOffset: { width: 0, height: 1 },
                        shadowOpacity: 0.1,
                        shadowRadius: 2,
                        elevation: 2,
                        borderWidth: 1,
                        borderColor: '#F3F4F6'
                    }}>
                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                            <View style={{
                                width: 40,
                                height: 40,
                                borderRadius: 20,
                                alignItems: 'center',
                                justifyContent: 'center',
                                backgroundColor: ORDER_STATUS[currentStatus].color,
                                marginRight: 12
                            }}>
                                <MaterialIcons
                                    name={currentStatus === 'PLACED' ? 'receipt-long' :
                                          currentStatus === 'OUT_FOR_DELIVERY' ? 'local-shipping' :
                                          ORDER_STATUS[currentStatus].icon}
                                    size={22}
                                    color="white"
                                />
                            </View>
                            <View style={{ flex: 1 }}>
                                <Text style={{ fontWeight: 'bold', fontSize: 14, color: '#1F2937' }}>
                                    {ORDER_STATUS[currentStatus].label}
                                </Text>
                                <Text style={{ color: '#4B5563', fontSize: 12, marginTop: 2 }}>
                                    {ORDER_STATUS[currentStatus].description}
                                </Text>
                            </View>
                        </View>
                    </View>
                )}

                {/* Simple Timeline UI */}
                <View style={{
                    backgroundColor: 'white',
                    borderRadius: 12,
                    padding: 16,
                    shadowColor: '#000',
                    shadowOffset: { width: 0, height: 1 },
                    shadowOpacity: 0.1,
                    shadowRadius: 2,
                    elevation: 2,
                    borderWidth: 1,
                    borderColor: '#F3F4F6'
                }}>
                    <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#1F2937', marginBottom: 16 }}>Order Tracking</Text>

                    {statusOrder.map((status, index) => {
                        const isCompleted = index < currentIndex;
                        const isCurrent = index === currentIndex;
                        const isPending = index > currentIndex;

                        // Determine the timestamp for this status if available
                        let timestamp = null;
                        if (status === 'DELIVERED' && order.deliveredAt) {
                            timestamp = new Date(order.deliveredAt);
                        } else if (status === 'OUT_FOR_DELIVERY' && order.deliveryStartedAt) {
                            timestamp = new Date(order.deliveryStartedAt);
                        } else if (status === 'PLACED' && order.date) {
                            timestamp = new Date(order.date);
                        }

                        // Format timestamp for display
                        const formattedTime = timestamp ?
                            timestamp.toLocaleTimeString("en-IN", { hour: '2-digit', minute: '2-digit', hour12: true }) : '';
                        const formattedDate = timestamp ?
                            timestamp.toLocaleDateString("en-IN", { day: 'numeric', month: 'short' }) : '';

                        return (
                            <View key={status} className="mb-4 last:mb-0">
                                <View className="flex-row">
                                    {/* Status Icon - Simple and Clean */}
                                    <View className="items-center mr-3">
                                        <View
                                            style={{
                                                width: 40,
                                                height: 40,
                                                borderRadius: 20,
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                backgroundColor: isCompleted ? '#22C55E' :
                                                                isCurrent ? ORDER_STATUS[status].color :
                                                                '#E5E7EB'
                                            }}
                                        >
                                            <MaterialIcons
                                                name={isCompleted ? 'check-circle' :
                                                      status === 'PLACED' ? 'receipt-long' :
                                                      status === 'OUT_FOR_DELIVERY' ? 'local-shipping' :
                                                      status === 'DELIVERED' ? 'done-all' :
                                                      ORDER_STATUS[status].icon}
                                                size={22}
                                                color={isPending ? '#9CA3AF' : 'white'}
                                            />
                                        </View>

                                        {/* Connecting Line - Simple */}
                                        {index < statusOrder.length - 1 && (
                                            <View style={{ height: 48, alignItems: 'center', justifyContent: 'center' }}>
                                                <View
                                                    style={{
                                                        width: 2,
                                                        height: '100%',
                                                        backgroundColor: isCompleted ? '#22C55E' : '#E5E7EB'
                                                    }}
                                                />
                                            </View>
                                        )}
                                    </View>

                                    {/* Status Content - Simple and Clean */}
                                    <View className="flex-1 ml-2">
                                        <View className="mb-2">
                                            <Text className={`font-bold text-sm ${
                                                isPending ? 'text-gray-400' :
                                                isCurrent ? 'text-madder' :
                                                'text-green-700'
                                            }`}>
                                                {ORDER_STATUS[status].label}
                                            </Text>

                                            {/* Status Description - Simplified */}
                                            <Text className={`text-xs mt-1 ${
                                                isPending ? 'text-gray-400' : 'text-gray-600'
                                            }`}>
                                                {ORDER_STATUS[status].description}
                                            </Text>

                                            {/* Timestamp - Simple */}
                                            {timestamp && (
                                                <View className="mt-1 flex-row items-center">
                                                    <MaterialIcons
                                                        name="schedule"
                                                        size={12}
                                                        color={isPending ? '#9CA3AF' : isCompleted ? '#10B981' : '#6B7280'}
                                                    />
                                                    <Text className={`ml-1 text-xs ${
                                                        isPending ? 'text-gray-400' :
                                                        isCompleted ? 'text-green-700' :
                                                        'text-gray-600'
                                                    }`}>
                                                        {formattedDate} at {formattedTime}
                                                    </Text>
                                                </View>
                                            )}
                                        </View>

                                        {/* Delivery Partner Info - Simplified */}
                                        {status === 'OUT_FOR_DELIVERY' && isCurrent && order.deliveryPartner && (
                                            <View style={{
                                                marginTop: 4,
                                                marginBottom: 8,
                                                backgroundColor: '#EEF2FF',
                                                padding: 8,
                                                borderRadius: 4,
                                                borderWidth: 1,
                                                borderColor: '#E0E7FF',
                                                alignSelf: 'flex-start',
                                                maxWidth: '80%'
                                            }}>
                                                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                                    <MaterialIcons name="local-shipping" size={12} color="#6366F1" />
                                                    <Text style={{
                                                        fontSize: 10,
                                                        marginLeft: 4,
                                                        color: '#4F46E5',
                                                        fontWeight: '500'
                                                    }}>
                                                        {order.deliveryPartner.name} is on the way
                                                    </Text>
                                                </View>
                                            </View>
                                        )}
                                    </View>

                                    {/* Removed Status Badge */}
                                </View>
                            </View>
                        );
                    })}
                </View>
            </View>
        );
    };

    const isValidDate = (dateString) => {
        if (!dateString) return false;
        const date = new Date(dateString);
        return !isNaN(date.getTime()) && date.getTime() > 0;
    };

    const renderOrderCard = (order, isCurrentOrder = true) => {
        // Format the order date if it's valid
        const orderDate = isValidDate(order.date)
            ? format(new Date(order.date), 'dd MMM yyyy')
            : "Invalid Date";

        // Format the order time if it's valid
        const orderTime = isValidDate(order.date)
            ? format(new Date(order.date), 'hh:mm a')
            : "";

        // Calculate the final price (with discounts applied)
        const originalPrice = order.originalAmount || order.total || 0;
        const couponDiscount = order.couponDiscount || 0;
        const coinsDiscount = order.coinsDiscount || 0;
        const finalPrice = order.totalAmount || (originalPrice - couponDiscount - coinsDiscount);

        // Check if order has a delivery partner assigned (current or past)
        const hasDeliveryPartner = order.deliveryPartner &&
            (order.status === 'CONFIRMED' || order.status === 'PREPARING' ||
             order.status === 'OUT_FOR_DELIVERY' || order.status === 'DELIVERED');

        return (
            <View
                key={order.id || order._id || `order-card-${Math.random()}`}
                className={`bg-white rounded-xl shadow-sm mb-4 overflow-hidden ${!isCurrentOrder ? 'border border-gray-100' : ''}`}
            >
                {/* Order Header - Improved UI */}
                <View className="p-4 border-b border-gray-100">
                    <View className="flex-row justify-between items-start">
                        <View className="flex-1">
                            <View className="flex-row items-center">
                                <View className="w-10 h-10 bg-madder/10 rounded-full items-center justify-center mr-3">
                                    <MaterialIcons name="shopping-bag" size={20} color="#A31621" />
                                </View>
                                <View>
                                    <Text className="text-base font-bold text-gray-800">
                                        Order #{order.orderNumber || order.id || order._id}
                                    </Text>
                                    <View className="flex-row items-center mt-1">
                                        <MaterialIcons name="event" size={12} color="#6B7280" />
                                        <Text className="text-gray-500 text-xs ml-1">
                                            {orderDate} • {orderTime}
                                        </Text>
                                    </View>
                                </View>
                            </View>

                            {/* Order Status Badge - Only for Current Orders */}
                            {isCurrentOrder && (
                                <View className="mt-3 self-start">
                                    <View className={`px-3 py-1 rounded-full ${
                                        order.status === 'DELIVERED' ? 'bg-green-100' :
                                        order.status === 'CANCELLED' ? 'bg-gray-100' :
                                        order.status === 'OUT_FOR_DELIVERY' ? 'bg-indigo-100' :
                                        order.status === 'PREPARING' ? 'bg-amber-100' :
                                        order.status === 'CONFIRMED' ? 'bg-blue-100' : 'bg-red-100'
                                    }`}>
                                        <Text className={`text-xs font-medium ${
                                            order.status === 'DELIVERED' ? 'text-green-700' :
                                            order.status === 'CANCELLED' ? 'text-gray-700' :
                                            order.status === 'OUT_FOR_DELIVERY' ? 'text-indigo-700' :
                                            order.status === 'PREPARING' ? 'text-amber-700' :
                                            order.status === 'CONFIRMED' ? 'text-blue-700' : 'text-red-700'
                                        }`}>
                                            {ORDER_STATUS[order.status]?.label || order.status}
                                        </Text>
                                    </View>
                                </View>
                            )}
                        </View>

                        {/* Price Summary */}
                        <View className="bg-white border border-gray-100 rounded-xl p-2 shadow-sm">
                            {(couponDiscount > 0 || coinsDiscount > 0) ? (
                                <View className="items-end">
                                    <Text className="text-gray-500 text-xs line-through">₹{originalPrice}</Text>
                                    <Text className="text-madder font-bold text-base">₹{finalPrice}</Text>
                                    <View className="bg-green-50 px-2 py-0.5 rounded-md mt-1">
                                        <Text className="text-green-700 text-xs font-medium">
                                            Saved ₹{couponDiscount + coinsDiscount}
                                        </Text>
                                    </View>
                                </View>
                            ) : (
                                <View className="items-end">
                                    <Text className="text-gray-500 text-xs">Total Amount</Text>
                                    <Text className="text-madder font-bold text-base">₹{finalPrice}</Text>
                                </View>
                            )}
                        </View>
                    </View>
                </View>

                {/* Order Items - Improved UI */}
                <View className="p-4 border-b border-gray-100">
                    <Text className="font-medium text-sm mb-3 text-gray-800">Order Items</Text>
                    {order.items && order.items.length > 0 ? order.items.map((item, index) => (
                        <View key={item.id || item._id || `item-${index}`} className="flex-row items-center mb-3">
                            <Image
                                source={item.image ?
                                    typeof item.image === 'string' ?
                                        { uri: item.image } : item.image
                                    : require('../assets/logo.png')}
                                className="w-12 h-12 rounded-lg"
                                resizeMode="cover"
                                onError={() => console.log('Failed to load item image')}
                            />
                            <View className="ml-3 flex-1">
                                <Text className="font-medium text-sm text-gray-800">{item?.name || 'Unknown Item'}</Text>
                                <View className="flex-row items-center mt-1">
                                    <View className="bg-gray-100 px-2 py-0.5 rounded-md">
                                        <Text className="text-gray-700 text-xs">{item.selectedWeight || 'Standard'}</Text>
                                    </View>
                                    <Text className="text-gray-500 text-xs ml-2">Qty: {item?.quantity || 1}</Text>
                                </View>
                                <View className="flex-row items-center justify-between mt-1">
                                    {item?.discount_price && item?.discount_price < item?.price ? (
                                        <View className="flex-row items-center">
                                            <Text className="text-gray-400 text-xs line-through mr-2">₹{item?.price || 0}</Text>
                                            <Text className="text-madder text-xs font-medium">₹{item?.discount_price || 0}</Text>
                                        </View>
                                    ) : (
                                        <Text className="text-madder text-xs font-medium">₹{item?.price || 0}</Text>
                                    )}
                                    <Text className="text-gray-700 text-xs font-medium">₹{item?.totalPrice || ((item?.quantity || 1) * (item?.discount_price || item?.price || 0))}</Text>
                                </View>
                            </View>
                        </View>
                    )) : (
                        <Text className="text-gray-500 text-sm">No items found</Text>
                    )}
                </View>

                {/* Bill Details Section - Matching CheckoutScreen */}
                <View className="p-4 border-b border-gray-100">
                    <View className="flex-row items-center mb-3">
                        <View className="w-8 h-8 bg-gray-100 rounded-full items-center justify-center mr-2">
                            <MaterialIcons name="receipt" size={16} color="#4B5563" />
                        </View>
                        <Text className="text-base font-bold text-gray-800">Bill Details</Text>
                    </View>

                    <View className="space-y-2">
                        <View className="flex-row justify-between items-center">
                            <Text className="text-gray-600 text-xs">Item Total</Text>
                            <Text className="font-medium text-xs text-gray-800">₹{order.originalAmount || order.total || 0}</Text>
                        </View>

                        <View className="flex-row justify-between items-center my-2">
                            <View className="flex-row items-center">
                                <View className="w-6 h-6 bg-blue-50 rounded-full items-center justify-center mr-2">
                                    <MaterialIcons name="local-shipping" size={16} color="#3B82F6" />
                                </View>
                                <Text className="text-gray-600 text-xs">Delivery Fee</Text>
                            </View>
                            {(order.originalAmount || order.total || 0) >= 499 ? (
                                <View className="flex-row items-center">
                                    <Text className="font-medium line-through text-gray-400 text-xs mr-1">
                                        ₹49
                                    </Text>
                                    <View className="bg-blue-50 px-2 py-0.5 rounded-md">
                                        <Text className="font-medium text-blue-700 text-xs">FREE</Text>
                                    </View>
                                </View>
                            ) : (
                                <Text className="font-medium text-xs text-gray-800">
                                    ₹49
                                </Text>
                            )}
                        </View>

                        {/* Display coupon discount if available */}
                        {order.couponDiscount > 0 && (
                            <View className="flex-row justify-between items-center">
                                <View className="flex-row items-center">
                                    <View className="w-6 h-6 bg-indigo-50 rounded-full items-center justify-center mr-2">
                                        <MaterialIcons name="local-offer" size={16} color="#6366F1" />
                                    </View>
                                    <Text className="text-gray-600 text-xs">
                                        Promo Code {order.appliedCoupon ? `(${order.appliedCoupon})` : ''}
                                    </Text>
                                </View>
                                <Text className="text-green-600 font-medium text-xs">
                                    -₹{order.couponDiscount}
                                </Text>
                            </View>
                        )}

                        {/* Display coins discount if available */}
                        {order.coinsDiscount > 0 && (
                            <View className="flex-row justify-between items-center">
                                <View className="flex-row items-center">
                                    <View className="w-6 h-6 bg-amber-50 rounded-full items-center justify-center mr-2">
                                        <MaterialIcons name="account-balance-wallet" size={16} color="#F59E0B" />
                                    </View>
                                    <Text className="text-gray-600 text-xs">Reward Coins</Text>
                                </View>
                                <Text className="text-green-600 font-medium text-xs">
                                    -₹{order.coinsDiscount}
                                </Text>
                            </View>
                        )}

                        {/* Display payment mode if available */}
                        {(order.paymentMode || order.paymentMethod) && (
                            <View className="flex-row justify-between items-center">
                                <View className="flex-row items-center">
                                    <View className="w-6 h-6 bg-green-50 rounded-full items-center justify-center mr-2">
                                        <MaterialIcons
                                            name={['COD', 'cod'].includes(order.paymentMode || order.paymentMethod) ? 'payments' : 'account-balance'}
                                            size={16}
                                            color="#10B981"
                                        />
                                    </View>
                                    <Text className="text-gray-600 text-xs">Payment Mode</Text>
                                </View>
                                <Text className="font-medium text-xs text-gray-800">
                                    {['COD', 'cod'].includes(order.paymentMode || order.paymentMethod) ? 'Cash on Delivery' :
                                     ['UPI', 'upi'].includes(order.paymentMode || order.paymentMethod) ? 'UPI Payment' :
                                     (order.paymentMode || order.paymentMethod)}
                                </Text>
                            </View>
                        )}

                        <View className="h-px bg-gray-100 my-2" />

                        <View className="flex-row justify-between items-center">
                            <Text className="text-gray-800 font-bold text-sm">Total Paid</Text>
                            <Text className="font-bold text-madder text-base">
                                ₹{order.totalAmount || order.total || 0}
                            </Text>
                        </View>

                        {/* Display coins earned if available */}
                        {order.coinsEarned > 0 && (
                            <View className="bg-amber-50 p-2 rounded-lg mt-2 border border-amber-100">
                                <View className="flex-row items-center">
                                    <MaterialIcons name="star" size={14} color="#F59E0B" />
                                    <Text className="text-amber-700 ml-1.5 text-xs font-medium">
                                        You earned {order.coinsEarned} Fresh coins from this order
                                    </Text>
                                </View>
                            </View>
                        )}
                    </View>
                </View>

                {/* Delivery Partner Info - Improved UI */}
                {hasDeliveryPartner && (
                    <View className="p-4 border-b border-gray-100">
                        <View className="flex-row items-center mb-3">
                            <View className="w-8 h-8 bg-indigo-100 rounded-full items-center justify-center mr-2">
                                <MaterialIcons name="person" size={16} color="#6366F1" />
                            </View>
                            <Text className="text-base font-bold text-gray-800">Delivery Partner</Text>
                        </View>

                        <TouchableOpacity
                            onPress={() => setExpandedDeliveryInfo(!expandedDeliveryInfo)}
                            className="bg-indigo-50 rounded-lg p-3 border border-indigo-100"
                        >
                            <View className="flex-row items-center justify-between">
                                <View className="flex-row items-center">
                                    <View className="w-10 h-10 rounded-full bg-white shadow-sm items-center justify-center">
                                        <MaterialIcons name="delivery-dining" size={20} color="#6366F1" />
                                    </View>
                                    <View className="ml-3">
                                        <Text className="font-medium text-gray-800 text-sm">{order.deliveryPartner?.name || "Assigned"}</Text>
                                        <Text className="text-indigo-700 text-xs mt-0.5">
                                            {order.deliveryPartner?.vehicleType || "Delivery Agent"}
                                        </Text>
                                    </View>
                                </View>
                                <View className="bg-white p-1 rounded-full">
                                    <MaterialIcons
                                        name={expandedDeliveryInfo ? "keyboard-arrow-up" : "keyboard-arrow-down"}
                                        size={20}
                                        color="#6366F1"
                                    />
                                </View>
                            </View>

                            {expandedDeliveryInfo && (
                                <View className="mt-3 pt-3 border-t border-indigo-100">
                                    {order.deliveryPartner?.phoneNumber ? (
                                        <TouchableOpacity
                                            className="flex-row items-center justify-between bg-white p-2 rounded-lg mt-2 shadow-sm"
                                            onPress={() => {
                                                Alert.alert(
                                                    "Make a call",
                                                    `Call delivery partner at +91 ${order.deliveryPartner.phoneNumber}?`,
                                                    [
                                                        {
                                                            text: "Cancel",
                                                            style: "cancel"
                                                        },
                                                        {
                                                            text: "Call",
                                                            onPress: () => Linking.openURL(`tel:${order.deliveryPartner.phoneNumber}`)
                                                        }
                                                    ]
                                                );
                                            }}
                                        >
                                            <View className="flex-row items-center">
                                                <MaterialIcons name="phone" size={16} color="#10B981" />
                                                <Text className="text-gray-700 ml-2 text-xs">+91 {order.deliveryPartner.phoneNumber}</Text>
                                            </View>
                                            <View className="bg-green-100 px-2 py-1 rounded-md">
                                                <Text className="text-green-700 text-xs font-medium">Call</Text>
                                            </View>
                                        </TouchableOpacity>
                                    ) : null}


                                </View>
                            )}
                        </TouchableOpacity>
                    </View>
                )}

                {/* Expected Delivery - Improved UI */}
                <View className="p-4 border-b border-gray-100">
                    <View className="flex-row items-center mb-3">
                        <View className="w-8 h-8 bg-madder/10 rounded-full items-center justify-center mr-2">
                            <MaterialIcons name="schedule" size={16} color="#A31621" />
                        </View>
                        <Text className="text-base font-bold text-gray-800">Expected Delivery</Text>
                    </View>

                    <View className="bg-snow rounded-lg p-3 border border-madder/10">
                        {order.expectedDeliveryInfo ? (
                            typeof order.expectedDeliveryInfo === 'string' ? (
                                <View className="flex-row items-center">
                                    <MaterialIcons name="delivery-dining" size={18} color="#A31621" />
                                    <Text className="text-madder font-medium ml-2 text-sm">{order.expectedDeliveryInfo}</Text>
                                </View>
                            ) : (
                                <View>
                                    <View className="flex-row items-center">
                                        <MaterialIcons name="event" size={14} color="#A31621" />
                                        <Text className="text-gray-700 font-medium ml-2 text-xs">
                                            {order.expectedDeliveryInfo.formattedDate ||
                                            (order.expectedDeliveryInfo.day === "Today"
                                                ? new Date().toLocaleDateString("en-IN", { day: "numeric", month: "short", year: "numeric" })
                                                : new Date(Date.now() + 86400000).toLocaleDateString("en-IN", { day: "numeric", month: "short", year: "numeric" }))}
                                        </Text>
                                    </View>
                                    <View className="flex-row items-center mt-2">
                                        <MaterialIcons name="access-time" size={14} color="#A31621" />
                                        <Text className="text-madder font-medium ml-2 text-xs">{order.expectedDeliveryInfo.time}</Text>
                                    </View>
                                </View>
                            )
                        ) : order.expectedDelivery ? (
                            <View>
                                <View className="flex-row items-center">
                                    <MaterialIcons name="event" size={14} color="#A31621" />
                                    <Text className="text-gray-700 font-medium ml-2 text-xs">
                                        {new Date(order.expectedDelivery).toLocaleDateString("en-IN", { day: "numeric", month: "short", year: "numeric" })}
                                    </Text>
                                </View>
                                <View className="flex-row items-center mt-2">
                                    <MaterialIcons name="access-time" size={14} color="#A31621" />
                                    <Text className="text-madder font-medium ml-2 text-xs">
                                        {new Date(order.expectedDelivery).toLocaleTimeString("en-IN", { hour: '2-digit', minute: '2-digit', hour12: true })}
                                    </Text>
                                </View>
                            </View>
                        ) : (
                            <View className="flex-row items-center">
                                <MaterialIcons name="info" size={14} color="#6B7280" />
                                <Text className="text-gray-500 ml-2 text-xs">Delivery time not specified</Text>
                            </View>
                        )}

                        {/* Add delivered time if available */}
                        {order.status === 'DELIVERED' && order.deliveredAt && (
                            <View className="mt-3 pt-3 border-t border-madder/10">
                                <View className="flex-row items-center">
                                    <MaterialIcons name="check-circle" size={14} color="#10B981" />
                                    <Text className="text-green-700 font-medium ml-2 text-xs">
                                        Delivered on: {new Date(order.deliveredAt).toLocaleDateString("en-IN", { day: "numeric", month: "short", year: "numeric" })}
                                        {" at "}
                                        {new Date(order.deliveredAt).toLocaleTimeString("en-IN", { hour: '2-digit', minute: '2-digit', hour12: true })}
                                    </Text>
                                </View>
                            </View>
                        )}
                    </View>
                </View>

                {/* Delivery Status - Only for Current Orders */}
                {isCurrentOrder && (
                    <View className="p-4">
                        <View className="flex-row items-center mb-3">
                            <View className="w-8 h-8 bg-indigo-100 rounded-full items-center justify-center mr-2">
                                <MaterialIcons name="local-shipping" size={16} color="#6366F1" />
                            </View>
                            <Text className="text-base font-bold text-gray-800">Delivery Status</Text>
                        </View>
                        {renderOrderStatus(order.status, order)}
                    </View>
                )}

                {/* Cancel Order Button - Only for current orders */}
                {isCurrentOrder && canCancelOrder(order.status) && (
                    <View className="px-4 pb-4">
                        <TouchableOpacity
                            className="bg-madder px-4 py-2 rounded-full"
                            onPress={() => handleCancelOrder(order)}
                        >
                            <Text className="text-white font-bold text-center">Cancel Order</Text>
                        </TouchableOpacity>
                    </View>
                )}
            </View>
        );
    };

    // Add this before the closing return statement
    return (
        <View className="flex-1 bg-gray-50">
            {/* Fixed Header */}
            <View className="bg-madder flex-row h-24 rounded-b-3xl p-4 pt-10 justify-between items-end">
                <View className="flex-row items-center">
                    <Text className="text-2xl text-white font-bold">Orders</Text>
                </View>

                {/* Add Continue Ordering button */}
                <TouchableOpacity
                    className="bg-white/20 px-3 py-1.5 rounded-full flex-row items-center"
                    onPress={() => navigation.navigate('MainTabs', { screen: 'Home' })}
                >
                    <Text className="text-white font-medium text-sm mr-1">Continue Ordering</Text>
                    <MaterialIcons name="arrow-forward" size={16} color="white" />
                </TouchableOpacity>
            </View>

            {/* Main Content */}
            <ScrollView
                ref={scrollViewRef}
                className="flex-1"
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{ padding: 16, paddingBottom: 20 }}
                refreshControl={
                    <RefreshControl
                        refreshing={isRefreshing}
                        onRefresh={() => fetchOrdersOptimized()}
                        colors={["#A31621"]}
                        tintColor="#A31621"
                        title="Pull to refresh orders"
                        titleColor="#666"
                    />
                }
            >
                <View className="flex-row items-center justify-between mb-4">
                    <Text className="text-xl font-bold text-gray-800">My orders</Text>
                    {isRefreshing && (
                        <View className="flex-row items-center">
                            <ActivityIndicator size="small" color="#A31621" />
                            <Text className="text-gray-500 ml-2 text-sm">Updating...</Text>
                        </View>
                    )}
                </View>

                {isInitialLoading ? (
                    <View className="flex-1 justify-center items-center p-4">
                        <ActivityIndicator size="large" color="#A31621" />
                        <Text className="text-gray-500 mt-4">Loading your orders...</Text>
                    </View>
                ) : orders.length === 0 ? (
                    <View className="flex-1 justify-center items-center p-4">
                        <MaterialIcons name="assignment" size={80} color="#D1D5DB" />
                        <Text className="text-xl text-gray-400 mt-6 text-center">
                            No orders found
                        </Text>
                        <Text className="text-gray-400 mt-2 text-center">
                            Place an order to see it here
                        </Text>
                        <TouchableOpacity
                            className="mt-6 bg-madder px-8 py-3 rounded-full"
                            onPress={() => navigation.navigate('Products')}
                        >
                            <Text className="text-white font-bold">Start Shopping</Text>
                        </TouchableOpacity>
                    </View>
                ) : (
                    <>
                        {/* Current Orders Section */}
                        {currentOrders.length > 0 && (
                            <>
                                <Text className="text-lg font-semibold text-gray-700 mb-3">Current Orders</Text>
                                {currentOrders.map(order => (
                                    <React.Fragment key={order.id || order._id}>
                                        {renderOrderCard(order, true)}
                                    </React.Fragment>
                                ))}
                            </>
                        )}

                        {/* Past Orders Section - Compact View */}
                        {pastOrders.length > 0 && (
                            <>
                                <Text className="text-lg font-semibold text-gray-700 mt-4 mb-3">Past Orders</Text>
                                {pastOrders.map(order => {
                                    // For past orders, use a compact view
                                    return (
                                        <React.Fragment key={order.id || order._id}>
                                            {order.status === 'DELIVERED' || order.status === 'CANCELLED'
                                                ? renderCompactPastOrder(order)
                                                : renderOrderCard(order, false)
                                            }
                                        </React.Fragment>
                                    );
                                })}
                            </>
                        )}
                    </>
                )}
            </ScrollView>

            {/* Cancel Order Modal - Enhanced with smooth animations */}
            <Modal
                transparent={true}
                visible={cancelModalVisible}
                animationType="none"
                onRequestClose={() => animateOut(() => setCancelModalVisible(false))}
            >
                <Animated.View
                    className="flex-1 bg-black/70 justify-center items-center p-5"
                    style={{ opacity: bgOpacityAnim }}
                >
                    <Animated.View
                        className="bg-white w-full rounded-2xl p-5 shadow-xl"
                        style={{
                            opacity: opacityAnim,
                            transform: [{ scale: scaleAnim }],
                            shadowColor: "#000",
                            shadowOffset: { width: 0, height: 10 },
                            shadowOpacity: 0.15,
                            shadowRadius: 20,
                            elevation: 10,
                        }}
                    >
                        <View className="items-center mb-4">
                            <Animated.View
                                className="w-16 h-16 bg-red-100 rounded-full items-center justify-center mb-3"
                                style={{ transform: [{ scale: iconScaleAnim }] }}
                            >
                                <MaterialIcons name="cancel" size={32} color="#A31621" />
                            </Animated.View>
                            <Text className="text-xl font-bold text-gray-800">Cancel Order</Text>
                            <Text className="text-gray-500 text-center mt-2">
                                Are you sure you want to cancel this order?
                            </Text>
                        </View>

                        {orderToCancel && (
                            <Animated.View
                                className="bg-gray-100 rounded-lg p-3 mb-4"
                                style={{ opacity: opacityAnim }}
                            >
                                <View className="flex-row justify-between mb-1">
                                    <Text className="text-gray-600">Order Number:</Text>
                                    <Text className="font-medium">#{orderToCancel.orderNumber || orderToCancel.id}</Text>
                                </View>
                                <View className="flex-row justify-between mb-1">
                                    <Text className="text-gray-600">Total Amount:</Text>
                                    <Text className="font-medium">₹{orderToCancel.calculatedTotal || orderToCancel.totalAmount || orderToCancel.total || 0}</Text>
                                </View>
                                <View className="flex-row justify-between">
                                    <Text className="text-gray-600">Status:</Text>
                                    <Text className="font-medium text-madder">
                                        {ORDER_STATUS[orderToCancel.status]?.label || orderToCancel.status}
                                    </Text>
                                </View>
                            </Animated.View>
                        )}

                        <View className="flex-row space-x-3">
                            <Animated.View style={{ flex: 1, transform: [{ scale: buttonScaleAnim }] }}>
                                <TouchableOpacity
                                    className="py-3 bg-gray-200 rounded-lg"
                                    onPress={() => animateOut(() => setCancelModalVisible(false))}
                                    activeOpacity={0.7}
                                >
                                    <Text className="text-gray-800 font-medium text-center">Keep Order</Text>
                                </TouchableOpacity>
                            </Animated.View>
                            <Animated.View style={{ flex: 1, transform: [{ scale: buttonScaleAnim }] }}>
                                <TouchableOpacity
                                    className="py-3 bg-madder rounded-lg"
                                    onPress={confirmCancelOrder}
                                    activeOpacity={0.7}
                                >
                                    <Text className="text-white font-bold text-center">Yes, Cancel</Text>
                                </TouchableOpacity>
                            </Animated.View>
                        </View>
                    </Animated.View>
                </Animated.View>
            </Modal>

            {/* Order Cancelled Success Modal - Enhanced with smooth animations */}
            <Modal
                transparent={true}
                visible={cancelSuccessModalVisible}
                animationType="none"
                onRequestClose={() => animateOut(() => setCancelSuccessModalVisible(false))}
            >
                <Animated.View
                    className="flex-1 bg-black/70 justify-center items-center p-5"
                    style={{ opacity: bgOpacityAnim }}
                >
                    <Animated.View
                        className="bg-white w-full rounded-2xl p-5 shadow-xl"
                        style={{
                            opacity: opacityAnim,
                            transform: [{ scale: scaleAnim }],
                            shadowColor: "#000",
                            shadowOffset: { width: 0, height: 10 },
                            shadowOpacity: 0.15,
                            shadowRadius: 20,
                            elevation: 10,
                        }}
                    >
                        <View className="items-center mb-4">
                            <Animated.View
                                className="w-16 h-16 bg-green-100 rounded-full items-center justify-center mb-3"
                                style={{ transform: [{ scale: iconScaleAnim }] }}
                            >
                                <MaterialIcons name="check-circle" size={32} color="#10B981" />
                            </Animated.View>
                            <Text className="text-xl font-bold text-gray-800">Order Cancelled</Text>
                            <Text className="text-gray-500 text-center mt-2">
                                Your order has been cancelled successfully!
                            </Text>
                        </View>

                        {cancelledOrderDetails && (
                            <Animated.View
                                className="bg-gray-100 rounded-lg p-3 mb-4"
                                style={{ opacity: opacityAnim }}
                            >
                                <View className="flex-row justify-between mb-1">
                                    <Text className="text-gray-600">Order Number:</Text>
                                    <Text className="font-medium">#{cancelledOrderDetails.orderNumber}</Text>
                                </View>
                                <View className="flex-row justify-between">
                                    <Text className="text-gray-600">Total Amount:</Text>
                                    <Text className="font-medium">₹{cancelledOrderDetails.total || 0}</Text>
                                </View>
                            </Animated.View>
                        )}

                        <Animated.View style={{ transform: [{ scale: buttonScaleAnim }] }}>
                            <TouchableOpacity
                                className="py-3 bg-madder rounded-lg"
                                onPress={() => animateOut(() => setCancelSuccessModalVisible(false))}
                                activeOpacity={0.7}
                            >
                                <Text className="text-white font-bold text-center">Close</Text>
                            </TouchableOpacity>
                        </Animated.View>
                    </Animated.View>
                </Animated.View>
            </Modal>

            {/* Order Detail Modal - Enhanced with smooth animations */}
            <Modal
                transparent={true}
                visible={detailModalVisible}
                animationType="none"
                onRequestClose={() => {
                    animateOut(() => {
                        setDetailModalVisible(false);
                        setSelectedOrder(null);
                    });
                }}
            >
                <Animated.View
                    className="flex-1 bg-black/70 justify-center items-center p-5"
                    style={{ opacity: bgOpacityAnim }}
                >
                    <Animated.View
                        className="bg-white w-full rounded-2xl shadow-xl"
                        style={{
                            opacity: opacityAnim,
                            transform: [{ scale: scaleAnim }],
                            maxHeight: '90%',
                            shadowColor: "#000",
                            shadowOffset: { width: 0, height: 10 },
                            shadowOpacity: 0.15,
                            shadowRadius: 20,
                            elevation: 10,
                        }}
                    >
                        {selectedOrderData && (
                            <>
                                {/* Modal Header */}
                                <View className="p-4 border-b border-gray-100 flex-row items-center justify-between">
                                    <View className="flex-row items-center">
                                        <View className="w-10 h-10 bg-madder/10 rounded-full items-center justify-center mr-3">
                                            <MaterialIcons name="shopping-bag" size={20} color="#A31621" />
                                        </View>
                                        <View>
                                            <Text className="text-lg font-bold text-gray-800">
                                                Order Details
                                            </Text>
                                            <View className="flex-row items-center">
                                                <Text className="text-gray-500 text-xs">
                                                    #{selectedOrderData.orderNumber || selectedOrderData.id}
                                                </Text>
                                                {selectedOrderData.date && (
                                                    <Text className="text-gray-500 text-xs ml-2">
                                                        • {new Date(selectedOrderData.date).toLocaleDateString("en-IN", { day: "numeric", month: "short" })} at {" "}
                                                        {new Date(selectedOrderData.date).toLocaleTimeString("en-IN", { hour: '2-digit', minute: '2-digit', hour12: true })}
                                                    </Text>
                                                )}
                                                <View className={`ml-2 px-2 py-0.5 rounded-full ${
                                                    selectedOrderData.status === 'DELIVERED' ? 'bg-green-100' :
                                                    selectedOrderData.status === 'CANCELLED' ? 'bg-gray-100' :
                                                    selectedOrderData.status === 'OUT_FOR_DELIVERY' ? 'bg-indigo-100' :
                                                    selectedOrderData.status === 'PREPARING' ? 'bg-amber-100' :
                                                    selectedOrderData.status === 'CONFIRMED' ? 'bg-blue-100' : 'bg-red-100'
                                                }`}>
                                                    <Text className={`text-xs font-medium ${
                                                        selectedOrderData.status === 'DELIVERED' ? 'text-green-700' :
                                                        selectedOrderData.status === 'CANCELLED' ? 'text-gray-700' :
                                                        selectedOrderData.status === 'OUT_FOR_DELIVERY' ? 'text-indigo-700' :
                                                        selectedOrderData.status === 'PREPARING' ? 'text-amber-700' :
                                                        selectedOrderData.status === 'CONFIRMED' ? 'text-blue-700' : 'text-red-700'
                                                    }`}>
                                                        {ORDER_STATUS[selectedOrderData.status]?.label || selectedOrderData.status}
                                                    </Text>
                                                </View>
                                            </View>
                                        </View>
                                    </View>
                                    <TouchableOpacity
                                        onPress={() => animateOut(() => {
                                            setDetailModalVisible(false);
                                            setSelectedOrder(null);
                                        })}
                                        className="w-8 h-8 bg-gray-100 rounded-full items-center justify-center"
                                    >
                                        <MaterialIcons name="close" size={18} color="#6B7280" />
                                    </TouchableOpacity>
                                </View>

                                {/* Modal Content - Scrollable (with hidden scrollbar) */}
                                <ScrollView
                                    className="max-h-full"
                                    showsVerticalScrollIndicator={false}
                                >
                                    {/* Order Status */}
                                    <View className="p-4 border-b border-gray-100">
                                        <View className={`p-3 rounded-lg ${
                                            selectedOrderData.status === 'DELIVERED' ? 'bg-green-50 border border-green-100' :
                                            selectedOrderData.status === 'CANCELLED' ? 'bg-gray-50 border border-gray-100' :
                                            selectedOrderData.status === 'OUT_FOR_DELIVERY' ? 'bg-indigo-50 border border-indigo-100' :
                                            selectedOrderData.status === 'PREPARING' ? 'bg-amber-50 border border-amber-100' :
                                            selectedOrderData.status === 'CONFIRMED' ? 'bg-blue-50 border border-blue-100' :
                                            'bg-red-50 border border-red-100'
                                        }`}>
                                            <View className="flex-row items-center">
                                                <View className="w-10 h-10 rounded-full bg-white items-center justify-center shadow-sm">
                                                    <MaterialIcons
                                                        name={ORDER_STATUS[selectedOrderData.status]?.icon || 'info'}
                                                        size={20}
                                                        color={ORDER_STATUS[selectedOrderData.status]?.color || '#6B7280'}
                                                    />
                                                </View>
                                                <View className="ml-3">
                                                    <Text className={`font-bold ${
                                                        selectedOrderData.status === 'DELIVERED' ? 'text-green-700' :
                                                        selectedOrderData.status === 'CANCELLED' ? 'text-gray-700' :
                                                        selectedOrderData.status === 'OUT_FOR_DELIVERY' ? 'text-indigo-700' :
                                                        selectedOrderData.status === 'PREPARING' ? 'text-amber-700' :
                                                        selectedOrderData.status === 'CONFIRMED' ? 'text-blue-700' :
                                                        'text-red-700'
                                                    }`}>
                                                        {ORDER_STATUS[selectedOrderData.status]?.label || selectedOrderData.status}
                                                    </Text>
                                                    <Text className="text-gray-600 text-xs mt-1">
                                                        {ORDER_STATUS[selectedOrderData.status]?.description || 'Status information not available'}
                                                    </Text>


                                                </View>
                                            </View>
                                        </View>
                                    </View>

                                    {/* Order Items */}
                                    <View className="p-4 border-b border-gray-100">
                                        <Text className="font-medium text-sm mb-3 text-gray-800">Order Items</Text>
                                        {selectedOrderData.items && selectedOrderData.items.length > 0 ? selectedOrderData.items.map((item, index) => (
                                            <View key={`detail-item-${item.id || item._id || index}`} className="flex-row items-center mb-3">
                                                <Image
                                                    source={item.image ?
                                                        typeof item.image === 'string' ?
                                                            { uri: item.image } : item.image
                                                        : require('../assets/logo.png')}
                                                    className="w-12 h-12 rounded-lg"
                                                    resizeMode="cover"
                                                    onError={() => console.log('Failed to load item image in modal')}
                                                />
                                                <View className="ml-3 flex-1">
                                                    <Text className="font-medium text-sm text-gray-800">{item?.name || 'Unknown Item'}</Text>
                                                    <View className="flex-row items-center mt-1">
                                                        <View className="bg-gray-100 px-2 py-0.5 rounded-md">
                                                            <Text className="text-gray-700 text-xs">{item.selectedWeight || 'Standard'}</Text>
                                                        </View>
                                                        <Text className="text-gray-500 text-xs ml-2">Qty: {item?.quantity || 1}</Text>
                                                    </View>
                                                    <View className="flex-row items-center justify-between mt-1">
                                                        {item?.discount_price && item?.discount_price < item?.price ? (
                                                            <View className="flex-row items-center">
                                                                <Text className="text-gray-400 text-xs line-through mr-2">₹{item?.price || 0}</Text>
                                                                <Text className="text-madder text-xs font-medium">₹{item?.discount_price || 0}</Text>
                                                            </View>
                                                        ) : (
                                                            <Text className="text-madder text-xs font-medium">₹{item?.price || 0}</Text>
                                                        )}
                                                        <Text className="text-gray-700 text-xs font-medium">₹{item?.totalPrice || ((item?.quantity || 1) * (item?.discount_price || item?.price || 0))}</Text>
                                                    </View>
                                                </View>
                                            </View>
                                        )) : (
                                            <Text className="text-gray-500 text-sm">No items found</Text>
                                        )}
                                    </View>

                                    {/* Bill Details */}
                                    <View className="p-4 border-b border-gray-100">
                                        <View className="flex-row items-center mb-3">
                                            <View className="w-8 h-8 bg-gray-100 rounded-full items-center justify-center mr-2">
                                                <MaterialIcons name="receipt" size={16} color="#4B5563" />
                                            </View>
                                            <Text className="text-base font-bold text-gray-800">Bill Details</Text>
                                        </View>

                                        <View className="space-y-2">
                                            <View className="flex-row justify-between items-center">
                                                <Text className="text-gray-600 text-xs">Item Total</Text>
                                                <Text className="font-medium text-xs text-gray-800">₹{selectedOrderData.originalAmount || selectedOrderData.total || 0}</Text>
                                            </View>

                                            <View className="flex-row justify-between items-center my-2">
                                                <View className="flex-row items-center">
                                                    <View className="w-6 h-6 bg-blue-50 rounded-full items-center justify-center mr-2">
                                                        <MaterialIcons name="local-shipping" size={16} color="#3B82F6" />
                                                    </View>
                                                    <Text className="text-gray-600 text-xs">Delivery Fee</Text>
                                                </View>
                                                {(selectedOrderData.originalAmount || selectedOrderData.total || 0) >= 499 ? (
                                                    <View className="flex-row items-center">
                                                        <Text className="font-medium line-through text-gray-400 text-xs mr-1">
                                                            ₹49
                                                        </Text>
                                                        <View className="bg-blue-50 px-2 py-0.5 rounded-md">
                                                            <Text className="font-medium text-blue-700 text-xs">FREE</Text>
                                                        </View>
                                                    </View>
                                                ) : (
                                                    <Text className="font-medium text-xs text-gray-800">
                                                        ₹49
                                                    </Text>
                                                )}
                                            </View>

                                            {/* Display coupon discount if available */}
                                            {selectedOrderData.couponDiscount > 0 && (
                                                <View className="flex-row justify-between items-center">
                                                    <View className="flex-row items-center">
                                                        <View className="w-6 h-6 bg-indigo-50 rounded-full items-center justify-center mr-2">
                                                            <MaterialIcons name="local-offer" size={16} color="#6366F1" />
                                                        </View>
                                                        <Text className="text-gray-600 text-xs">
                                                            Promo Code {selectedOrderData.appliedCoupon ? `(${selectedOrderData.appliedCoupon})` : ''}
                                                        </Text>
                                                    </View>
                                                    <Text className="text-green-600 font-medium text-xs">
                                                        -₹{selectedOrderData.couponDiscount}
                                                    </Text>
                                                </View>
                                            )}

                                            {/* Display coins discount if available */}
                                            {selectedOrderData.coinsDiscount > 0 && (
                                                <View className="flex-row justify-between items-center">
                                                    <View className="flex-row items-center">
                                                        <View className="w-6 h-6 bg-amber-50 rounded-full items-center justify-center mr-2">
                                                            <MaterialIcons name="account-balance-wallet" size={16} color="#F59E0B" />
                                                        </View>
                                                        <Text className="text-gray-600 text-xs">Reward Coins</Text>
                                                    </View>
                                                    <Text className="text-green-600 font-medium text-xs">
                                                        -₹{selectedOrderData.coinsDiscount}
                                                    </Text>
                                                </View>
                                            )}

                                            {/* Display payment mode if available */}
                                            {(selectedOrderData.paymentMode || selectedOrderData.paymentMethod) && (
                                                <View className="flex-row justify-between items-center">
                                                    <View className="flex-row items-center">
                                                        <View className="w-6 h-6 bg-green-50 rounded-full items-center justify-center mr-2">
                                                            <MaterialIcons
                                                                name={['COD', 'cod'].includes(selectedOrderData.paymentMode || selectedOrderData.paymentMethod) ? 'payments' : 'account-balance'}
                                                                size={16}
                                                                color="#10B981"
                                                            />
                                                        </View>
                                                        <Text className="text-gray-600 text-xs">Payment Mode</Text>
                                                    </View>
                                                    <Text className="font-medium text-xs text-gray-800">
                                                        {['COD', 'cod'].includes(selectedOrderData.paymentMode || selectedOrderData.paymentMethod) ? 'Cash on Delivery' :
                                                         ['UPI', 'upi'].includes(selectedOrderData.paymentMode || selectedOrderData.paymentMethod) ? 'UPI Payment' :
                                                         (selectedOrderData.paymentMode || selectedOrderData.paymentMethod)}
                                                    </Text>
                                                </View>
                                            )}

                                            <View className="h-px bg-gray-100 my-2" />

                                            <View className="flex-row justify-between items-center">
                                                <Text className="text-gray-800 font-bold text-sm">Total Paid</Text>
                                                <Text className="font-bold text-madder text-base">
                                                    ₹{selectedOrderData.totalAmount || selectedOrderData.total || 0}
                                                </Text>
                                            </View>

                                            {/* Display coins earned if available */}
                                            {selectedOrderData.coinsEarned > 0 && (
                                                <View className="bg-amber-50 p-2 rounded-lg mt-2 border border-amber-100">
                                                    <View className="flex-row items-center">
                                                        <MaterialIcons name="star" size={14} color="#F59E0B" />
                                                        <Text className="text-amber-700 ml-1.5 text-xs font-medium">
                                                            You earned {selectedOrderData.coinsEarned} Fresh coins from this order
                                                        </Text>
                                                    </View>
                                                </View>
                                            )}
                                        </View>
                                    </View>

                                    {/* Delivery Partner Info */}
                                    {selectedOrderData.deliveryPartner && (
                                        <View className="p-4 border-b border-gray-100">
                                            <View className="flex-row items-center mb-3">
                                                <View className="w-8 h-8 bg-indigo-100 rounded-full items-center justify-center mr-2">
                                                    <MaterialIcons name="person" size={16} color="#6366F1" />
                                                </View>
                                                <Text className="text-base font-bold text-gray-800">Delivery Partner</Text>
                                            </View>

                                            <View className="bg-indigo-50 rounded-lg p-3 border border-indigo-100">
                                                <View className="flex-row items-center">
                                                    <View className="w-10 h-10 rounded-full bg-white shadow-sm items-center justify-center">
                                                        <MaterialIcons name="delivery-dining" size={20} color="#6366F1" />
                                                    </View>
                                                    <View className="ml-3">
                                                        <Text className="font-medium text-gray-800 text-sm">{selectedOrderData.deliveryPartner?.name || "Assigned"}</Text>
                                                        <Text className="text-indigo-700 text-xs mt-0.5">
                                                            {selectedOrderData.deliveryPartner?.vehicleType || "Delivery Agent"}
                                                        </Text>
                                                    </View>
                                                </View>

                                                {selectedOrderData.deliveryPartner?.phoneNumber &&
                                                 (selectedOrderData.status === 'OUT_FOR_DELIVERY' || selectedOrderData.status === 'DELIVERED') && (
                                                    <View className="bg-white p-2 rounded-lg mt-3 shadow-sm">
                                                        <View className="flex-row items-center">
                                                            <MaterialIcons name="phone" size={16} color="#10B981" />
                                                            <Text className="text-gray-700 ml-2 text-xs">+91 {selectedOrderData.deliveryPartner.phoneNumber}</Text>
                                                        </View>
                                                    </View>
                                                )}
                                            </View>
                                        </View>
                                    )}

                                    {/* Delivery Address */}
                                    {selectedOrderData.deliveryAddress && (
                                        <View className="p-4 border-b border-gray-100">
                                            <View className="flex-row items-center mb-3">
                                                <View className="w-8 h-8 bg-madder/10 rounded-full items-center justify-center mr-2">
                                                    <MaterialIcons name="location-on" size={16} color="#A31621" />
                                                </View>
                                                <Text className="text-base font-bold text-gray-800">Delivery Address</Text>
                                            </View>

                                            <View className="bg-snow rounded-lg p-3 border border-madder/10">
                                                <Text className="text-gray-700 text-xs">
                                                    {selectedOrderData.deliveryAddress.fullAddress ||
                                                    `${selectedOrderData.deliveryAddress.doorNo || ''}${selectedOrderData.deliveryAddress.streetName ? `, ${selectedOrderData.deliveryAddress.streetName}` : ''}${selectedOrderData.deliveryAddress.area ? `, ${selectedOrderData.deliveryAddress.area}` : ''}${selectedOrderData.deliveryAddress.district ? `, ${selectedOrderData.deliveryAddress.district}` : ''}${selectedOrderData.deliveryAddress.pincode ? ` - ${selectedOrderData.deliveryAddress.pincode}` : ''}`}
                                                </Text>


                                            </View>
                                        </View>
                                    )}

                                    {/* Delivery Status Timeline - Only for Current Orders */}
                                    {selectedOrderData.status !== 'DELIVERED' && selectedOrderData.status !== 'CANCELLED' && (
                                        <View className="p-4 border-b border-gray-100">
                                            <View className="flex-row items-center mb-3">
                                                <View className="w-8 h-8 bg-indigo-100 rounded-full items-center justify-center mr-2">
                                                    <MaterialIcons name="local-shipping" size={16} color="#6366F1" />
                                                </View>
                                                <Text className="text-base font-bold text-gray-800">Order Tracking</Text>
                                            </View>
                                            {renderOrderStatus(selectedOrderData.status, selectedOrderData)}
                                        </View>
                                    )}

                                    {/* Expected/Actual Delivery Time */}
                                    <View className="p-4">
                                        <View className="flex-row items-center mb-3">
                                            <View className="w-8 h-8 bg-madder/10 rounded-full items-center justify-center mr-2">
                                                <MaterialIcons name="schedule" size={16} color="#A31621" />
                                            </View>
                                            <Text className="text-base font-bold text-gray-800">
                                                {selectedOrderData.status === 'DELIVERED' ? 'Delivery Information' : 'Expected Delivery'}
                                            </Text>
                                        </View>

                                        <View className="bg-snow rounded-lg p-3 border border-madder/10">
                                            {/* Expected Delivery */}
                                            {selectedOrderData.expectedDeliveryInfo && selectedOrderData.status !== 'DELIVERED' && (
                                                <View>
                                                    <Text className="font-medium text-xs text-gray-700 mb-1">Expected delivery:</Text>
                                                    {typeof selectedOrderData.expectedDeliveryInfo === 'string' ? (
                                                        <Text className="text-madder text-sm">{selectedOrderData.expectedDeliveryInfo}</Text>
                                                    ) : (
                                                        <View className="flex-row items-center">
                                                            <MaterialIcons name="event" size={14} color="#A31621" />
                                                            <Text className="text-madder ml-2 text-sm">
                                                                {selectedOrderData.expectedDeliveryInfo.formattedDate}, {selectedOrderData.expectedDeliveryInfo.time}
                                                            </Text>
                                                        </View>
                                                    )}
                                                </View>
                                            )}

                                            {/* Actual Delivery */}
                                            {selectedOrderData.status === 'DELIVERED' && (
                                                <View>
                                                    <Text className="font-medium text-xs text-gray-700 mb-1">Delivered on:</Text>
                                                    <View className="flex-row items-center">
                                                        <MaterialIcons name="check-circle" size={14} color="#10B981" />
                                                        <Text className="text-green-700 ml-2 text-sm">
                                                            {selectedOrderData.deliveredAt ?
                                                                format(new Date(selectedOrderData.deliveredAt), 'dd MMM yyyy, hh:mm a') :
                                                                (isValidDate(selectedOrderData.date) ?
                                                                    format(new Date(selectedOrderData.date), 'dd MMM yyyy, hh:mm a') :
                                                                    'Delivery date not recorded')}
                                                        </Text>
                                                    </View>
                                                </View>
                                            )}

                                            {/* Cancelled Order */}
                                            {selectedOrderData.status === 'CANCELLED' && (
                                                <View>
                                                    <Text className="font-medium text-xs text-gray-700 mb-1">Order cancelled:</Text>
                                                    <View className="flex-row items-center">
                                                        <MaterialIcons name="cancel" size={14} color="#6B7280" />
                                                        <Text className="text-gray-700 ml-2 text-sm">
                                                            {selectedOrderData.cancelledAt ?
                                                                format(new Date(selectedOrderData.cancelledAt), 'dd MMM yyyy, hh:mm a') :
                                                                'Cancellation date not recorded'}
                                                        </Text>
                                                    </View>
                                                </View>
                                            )}
                                        </View>
                                    </View>
                                </ScrollView>

                                {/* Modal Footer */}
                                <View className="p-4 border-t border-gray-100">
                                    <TouchableOpacity
                                        className="py-3 bg-madder rounded-lg"
                                        onPress={() => animateOut(() => {
                                            setDetailModalVisible(false);
                                            setSelectedOrder(null);
                                        })}
                                    >
                                        <Text className="text-white font-bold text-center">Close</Text>
                                    </TouchableOpacity>
                                </View>
                            </>
                        )}
                    </Animated.View>
                </Animated.View>
            </Modal>

            {/* Remove the custom BottomNavigator component */}
        </View>
    );
};

// Remove the BottomNavigator component definition

export default OrdersScreen;