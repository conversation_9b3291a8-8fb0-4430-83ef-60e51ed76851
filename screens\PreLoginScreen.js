import { View, Text, TextInput, TouchableOpacity, Image, KeyboardAvoidingView, Platform, ScrollView, Animated, ActivityIndicator, Alert, Keyboard } from 'react-native';
import React, { useState, useRef, useEffect } from 'react';
import { MaterialIcons } from "@expo/vector-icons";
import { useUser } from '../context/UserContext';
import axios from 'axios';
import { API_URL } from '../config/constants';
import PolicyModal from '../component/PolicyModal';
import {
    registerForPushNotificationsAsync,
    savePushTokenToBackend,
    requestNotificationPermissionWithMessage,
    getNotificationDebugInfo,
    showNotificationSetupGuidance,
    testNotificationSetup
} from '../utils/notificationUtils';

const PreLoginScreen = ({ navigation }) => {
    const [mobile, setMobile] = useState('');
    const [showAlert, setShowAlert] = useState(false);
    const [alertMessage, setAlertMessage] = useState('Please enter a valid 10-digit mobile number.');
    const [loading, setLoading] = useState(false);
    const [pushToken, setPushToken] = useState(null);
    const [showConfirmation, setShowConfirmation] = useState(false);
    const [showPrivacyModal, setShowPrivacyModal] = useState(false);
    const [showTermsModal, setShowTermsModal] = useState(false);
    const alertAnimation = useRef(new Animated.Value(0)).current;
    const confirmationAnimation = useRef(new Animated.Value(0)).current;
    // We don't need any user context functions here
    const { } = useUser();

    // Register for push notifications on component mount
    useEffect(() => {
        const setupPushNotifications = async () => {
            try {
                console.log('Setting up push notifications...');

                // Get debug info first
                const debugInfo = await getNotificationDebugInfo();
                console.log('Notification debug info:', debugInfo);

                const token = await registerForPushNotificationsAsync();
                if (token) {
                    setPushToken(token);
                    console.log('Push token registered:', token);
                } else {
                    console.log('Failed to get push token');
                    // Show guidance if setup failed
                    setTimeout(() => {
                        showNotificationSetupGuidance();
                    }, 1000);
                }
            } catch (error) {
                console.error('Error setting up push notifications:', error);
                // Show guidance on error
                setTimeout(() => {
                    showNotificationSetupGuidance();
                }, 1000);
            }
        };

        setupPushNotifications();
    }, []);

    const showCustomAlert = (message) => {
        setAlertMessage(message || 'Please enter a valid 10-digit mobile number.');
        setShowAlert(true);
        Animated.sequence([
            Animated.timing(alertAnimation, {
                toValue: 1,
                duration: 300,
                useNativeDriver: true,
            }),
            Animated.delay(2000),
            Animated.timing(alertAnimation, {
                toValue: 0,
                duration: 300,
                useNativeDriver: true,
            })
        ]).start(() => setShowAlert(false));
    };

    const handleOtpRequest = () => {
        if (!/^\d{10}$/.test(mobile)) {
            showCustomAlert();
            return;
        }

        // Hide keyboard before showing confirmation dialog
        Keyboard.dismiss();

        // Testing shortcut: Skip confirmation for test number
        if (mobile === '8778194599') {
            console.log('Testing shortcut - bypassing confirmation for test number');
            proceedWithOtp();
            return;
        }

        // Show confirmation dialog before proceeding
        setShowConfirmation(true);
        Animated.timing(confirmationAnimation, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
        }).start();
    };

    const proceedWithOtp = async () => {
        // Hide confirmation dialog
        Animated.timing(confirmationAnimation, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
        }).start(() => setShowConfirmation(false));

        setLoading(true);

        try {
            // Save push token to backend if available
            if (pushToken) {
                console.log('Saving push token to backend...');
                const tokenSaved = await savePushTokenToBackend(mobile, pushToken);
                if (tokenSaved) {
                    console.log('Push token saved successfully');
                } else {
                    console.log('Failed to save push token, but continuing...');
                }
            } else {
                console.log('No push token available, requesting permission...');
                const permissionGranted = await requestNotificationPermissionWithMessage(
                    'Enable notifications to receive OTP instantly without typing!'
                );
                if (permissionGranted) {
                    const newToken = await registerForPushNotificationsAsync();
                    if (newToken) {
                        setPushToken(newToken);
                        await savePushTokenToBackend(mobile, newToken);
                    }
                }
            }

            console.log(`Checking if user exists with phone number: ${mobile}`);
            console.log(`API URL: ${API_URL}/auth/check-user`);

            try {
                // First check if the user exists and get their type
                console.log('Sending request to check user...');
                console.log('Request data:', { phoneNumber: mobile });

                // Check if user exists
                const checkResponse = await axios.post(`${API_URL}/auth/check-user`, {
                    phoneNumber: mobile
                }, {
                    timeout: 10000, // 10 second timeout
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                console.log('Check user response status:', checkResponse.status);
                console.log('Check user response data:', checkResponse.data);

                // Special handling for admin and delivery partner numbers
                if (checkResponse.data.userType === 'ADMIN') {
                    console.log('Admin user detected!');
                } else if (checkResponse.data.userType === 'DELIVERY_PARTNER') {
                    console.log('Delivery partner detected!');
                }

                console.log(`Sending OTP to phone number: ${mobile}`);
                console.log(`API URL: ${API_URL}/auth/send-otp`);

                // Send OTP to the user
                console.log('Sending request for OTP...');
                const otpResponse = await axios.post(`${API_URL}/auth/send-otp`, {
                    phoneNumber: mobile
                }, {
                    timeout: 10000, // 10 second timeout
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                console.log('OTP response status:', otpResponse.status);
                console.log('OTP response data:', otpResponse.data);

                // For debugging, log the OTP
                if (otpResponse.data.otp) {
                    console.log('OTP for testing:', otpResponse.data.otp);
                }

                console.log('Navigating to OTPScreen with params:', {
                    phoneNumber: mobile,
                    userType: checkResponse.data.userType || 'USER',
                    exists: checkResponse.data.exists,
                    otp: otpResponse.data.otp,
                    isNewUser: otpResponse.data.isNewUser,
                    pushNotificationSent: otpResponse.data.pushNotificationSent,
                    hasPushToken: !!pushToken
                });

                // Navigate to OTP screen with the user type information
                navigation.navigate('OTPScreen', {
                    phoneNumber: mobile,
                    userType: checkResponse.data.userType || 'USER',
                    exists: checkResponse.data.exists,
                    otp: otpResponse.data.otp, // For development only, remove in production
                    isNewUser: otpResponse.data.isNewUser,
                    pushNotificationSent: otpResponse.data.pushNotificationSent,
                    hasPushToken: !!pushToken
                });
            } catch (error) {
                console.error('API Error:', error.message);
                let errorMessage = 'Failed to connect to the server. Please try again later.';

                if (error.response) {
                    console.error('Response status:', error.response.status);
                    console.error('Response data:', error.response.data);

                    // Simplified error handling - no strict rate limiting for phone validation
                    if (error.response.data?.message) {
                        errorMessage = error.response.data.message;
                    }
                }

                // Show alert to the user
                showCustomAlert(errorMessage);
                setLoading(false);
            }
        } finally {
            setLoading(false);
        }
    };

    const handleEditNumber = () => {
        // Hide confirmation dialog and allow user to edit
        Animated.timing(confirmationAnimation, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
        }).start(() => setShowConfirmation(false));
    };

    const handleCancelConfirmation = () => {
        // Hide confirmation dialog
        Animated.timing(confirmationAnimation, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
        }).start(() => setShowConfirmation(false));
    };

    const handleMobileChange = (text) => {
        // Only allow digits
        const numbersOnly = text.replace(/[^0-9]/g, '');
        setMobile(numbersOnly);
    };

    return (
        <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            className="flex-1 bg-white"
        >
            {/* Unique asymmetric design elements */}
            <View className="absolute top-0 right-0 w-[70%] h-[220px] overflow-hidden">
                <View className="absolute w-[300px] h-[300px] rounded-full bg-madder opacity-[0.08] top-[-100px] right-[-50px]" />
            </View>

            <View className="absolute bottom-0 left-0 w-[50%] h-[200px] overflow-hidden">
                <View className="absolute w-[250px] h-[250px] rounded-full bg-madder opacity-[0.05] bottom-[-100px] left-[-50px]" />
            </View>

            <ScrollView
                className="flex-1"
                contentContainerClassName="px-6 pt-12 pb-6"
                keyboardShouldPersistTaps="handled"
            >
                {/* Logo with unique presentation */}
                <View className="items-center mt-16 mb-10">
                    <View className="w-[80px] h-[80px] rounded-xl bg-white items-center justify-center shadow-md"
                        style={{
                            transform: [{ rotate: '-5deg' }],
                            elevation: 4,
                            shadowColor: '#000',
                            shadowOffset: { width: 0, height: 2 },
                            shadowOpacity: 0.1,
                            shadowRadius: 4,
                        }}>
                        <Image
                            source={require('../assets/logo.png')}
                            className="w-[60px] h-[60px]"
                            resizeMode="contain"
                        />
                    </View>
                </View>

                {/* Header text */}
                <View className="mb-8">
                    <Text className="text-2xl font-bold text-gray-800 text-center mb-2">
                        Login
                    </Text>
                    <Text className="text-sm text-gray-600 text-center">
                        Login to grab the freshest cuts delivered to your door
                    </Text>
                </View>

                {/* Minimal Phone Input Field */}
                <View className="mb-8 px-6">
                    <View className="flex-row items-center border border-gray-200 rounded-xl px-4 py-4 bg-white"
                        style={{
                            elevation: 2,
                            shadowColor: '#000',
                            shadowOffset: { width: 0, height: 1 },
                            shadowOpacity: 0.05,
                            shadowRadius: 2,
                        }}>
                        <Text className="text-base font-medium text-gray-700 mr-3">+91</Text>
                        <View className="w-[1px] h-6 bg-gray-200 mr-3" />
                        <TextInput
                            placeholder="Enter Mobile Number"
                            keyboardType="phone-pad"
                            className="flex-1 text-base text-gray-800"
                            value={mobile}
                            onChangeText={handleMobileChange}
                            placeholderTextColor="#9CA3AF"
                            maxLength={10}
                        />
                        {mobile.length === 10 && (
                            <MaterialIcons name="check-circle" size={20} color="#16A34A" />
                        )}
                    </View>
                </View>

                {/* Simple Continue Button */}
                <View className="px-6 mb-6">
                    <TouchableOpacity
                        className="bg-madder py-4 rounded-xl items-center"
                        style={{
                            elevation: 3,
                            shadowColor: '#000',
                            shadowOffset: { width: 0, height: 2 },
                            shadowOpacity: 0.1,
                            shadowRadius: 3,
                            opacity: mobile.length === 10 ? 1 : 0.5,
                        }}
                        onPress={handleOtpRequest}
                        disabled={loading || mobile.length !== 10}
                    >
                        {loading ? (
                            <ActivityIndicator size="small" color="#FFFFFF" />
                        ) : (
                            <Text className="text-white text-base font-semibold">
                                Continue
                            </Text>
                        )}
                    </TouchableOpacity>
                </View>

                {/* Alert message */}
                {showAlert && (
                    <Animated.View
                        className="mt-6 w-full px-4"
                        style={{
                            opacity: alertAnimation,
                            transform: [{
                                translateY: alertAnimation.interpolate({
                                    inputRange: [0, 1],
                                    outputRange: [10, 0]
                                })
                            }]
                        }}
                    >
                        <View className="bg-red-50 rounded-lg shadow-sm py-3 px-4 flex-row items-center border-l-4 border-l-red-500">
                            <MaterialIcons name="error-outline" size={20} color="#DC2626" />
                            <Text className="text-red-700 font-medium ml-2 text-sm">
                                {alertMessage}
                            </Text>
                        </View>
                    </Animated.View>
                )}

                {/* Login notes */}
                <View className="px-8 mt-4 space-y-2">

                    <Text className="text-gray-500 text-xs text-center mt-2">
                        By continuing, you agree to our{' '}
                        <Text
                            className="text-madder font-medium underline"
                            onPress={() => {
                                // Add small haptic feedback for better UX
                                if (typeof Haptics !== 'undefined') {
                                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                                }
                                setShowTermsModal(true);
                            }}
                            suppressHighlighting={true}
                        >
                            Terms of Service
                        </Text>{' '}
                        and{' '}
                        <Text
                            className="text-madder font-medium underline"
                            onPress={() => {
                                // Add small haptic feedback for better UX
                                if (typeof Haptics !== 'undefined') {
                                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                                }
                                setShowPrivacyModal(true);
                            }}
                            suppressHighlighting={true}
                        >
                            Privacy Policy
                        </Text>
                    </Text>
                </View>
            </ScrollView>

            {/* Minimal Phone Number Confirmation Dialog */}
            {showConfirmation && (
                <TouchableOpacity
                    style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        backgroundColor: 'rgba(0, 0, 0, 0.5)',
                        justifyContent: 'center',
                        alignItems: 'center',
                        zIndex: 10000,
                    }}
                    activeOpacity={1}
                    onPress={handleCancelConfirmation}
                >
                    <TouchableOpacity
                        style={{
                            opacity: confirmationAnimation,
                            transform: [{
                                scale: confirmationAnimation.interpolate({
                                    inputRange: [0, 1],
                                    outputRange: [0.9, 1]
                                })
                            }]
                        }}
                        className="bg-white mx-6 rounded-2xl p-6 shadow-2xl"
                        style={{
                            elevation: 10,
                            shadowColor: '#000',
                            shadowOffset: { width: 0, height: 4 },
                            shadowOpacity: 0.25,
                            shadowRadius: 8,
                        }}
                        activeOpacity={1}
                        onPress={() => {}}
                    >
                        {/* Simple Header */}
                        <View className="items-center mb-6">
                            <View className="w-16 h-16 bg-madder/10 rounded-full items-center justify-center mb-4">
                                <MaterialIcons name="phone" size={32} color="#A31621" />
                            </View>
                            <Text className="text-xl font-bold text-gray-800 text-center">
                                Confirm Your Number
                            </Text>
                        </View>

                        {/* Clean Phone Number Display */}
                        <View className="bg-gray-50 rounded-xl p-4 mb-6">
                            <Text className="text-center text-gray-600 text-sm mb-2">
                                We'll send OTP to this number and use this to call while delivery the order:
                            </Text>
                            <Text className="text-center text-2xl font-bold text-madder">
                                +91 {mobile}
                            </Text>
                        </View>

                        {/* Simple Action Buttons */}
                        <View className="space-y-3">
                            <TouchableOpacity
                                onPress={proceedWithOtp}
                                disabled={loading}
                                className="w-full py-4 bg-madder rounded-xl items-center"
                                style={{
                                    elevation: 3,
                                    shadowColor: '#000',
                                    shadowOffset: { width: 0, height: 2 },
                                    shadowOpacity: 0.1,
                                    shadowRadius: 3,
                                    opacity: loading ? 0.7 : 1
                                }}
                            >
                                {loading ? (
                                    <ActivityIndicator size="small" color="#FFFFFF" />
                                ) : (
                                    <Text className="text-white font-semibold text-lg">
                                        Send OTP
                                    </Text>
                                )}
                            </TouchableOpacity>

                            <TouchableOpacity
                                onPress={handleEditNumber}
                                disabled={loading}
                                className="w-full py-4 bg-gray-100 rounded-xl items-center"
                            >
                                <Text className="text-gray-700 font-medium">
                                    Edit Number
                                </Text>
                            </TouchableOpacity>
                        </View>
                    </TouchableOpacity>
                </TouchableOpacity>
            )}

            {/* Privacy Policy Modal */}
            <PolicyModal
                visible={showPrivacyModal}
                onClose={() => setShowPrivacyModal(false)}
                type="privacy"
            />

            {/* Terms & Conditions Modal */}
            <PolicyModal
                visible={showTermsModal}
                onClose={() => setShowTermsModal(false)}
                type="terms"
            />
        </KeyboardAvoidingView>
    );
};

export default PreLoginScreen;
