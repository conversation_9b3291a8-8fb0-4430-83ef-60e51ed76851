import { View, Text, Image, ScrollView, TouchableOpacity, Dimensions, Share, StatusBar, Alert } from 'react-native';
import React, { useState, useEffect } from 'react';
import { MaterialIcons } from "@expo/vector-icons";
import { useCart } from '../context/CartContext';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import AddToCartModal from '../Components/AddToCartModal';
import NutritionFacts from '../Components/NutritionFacts';
const { width } = Dimensions.get('window');

const ProductDetailScreen = ({ route, navigation }) => {
  const { product } = route.params;

  // Determine the product's base weight (default to 500g if not specified)
  const getProductBaseWeight = () => {
    if (product.weight) {
      // Extract numeric value from weight string (e.g., "1kg" -> 1000, "500g" -> 500)
      const weightStr = product.weight.toLowerCase();
      if (weightStr.includes('kg')) {
        return parseInt(weightStr) * 1000; // Convert kg to grams
      } else if (weightStr.includes('g')) {
        return parseInt(weightStr); // Already in grams
      }
    }
    return 500; // Default to 500g
  };

  const productBaseWeight = getProductBaseWeight();
  const baseWeightLabel = productBaseWeight >= 1000 ? `${productBaseWeight/1000}kg` : `${productBaseWeight}g`;

  const [selectedWeight, setSelectedWeight] = useState(baseWeightLabel);
  const [quantity, setQuantity] = useState(1);
  const {
    addToCart,
    addToCartModal,
    closeAddToCartModal,
    proceedToCart
  } = useCart();

  // Check if product is available
  const isProductAvailable = product.available !== false && product.isAvailable !== false;

  // Calculate base price and discount price
  const basePrice = product.price;
  const discountPrice = product.discount_price || null;

  // Calculate discount percentage if not provided
  const [discountPercentage, setDiscountPercentage] = useState(0);

  useEffect(() => {
    // Calculate discount percentage if discount_price exists
    if (basePrice && discountPrice && basePrice > discountPrice) {
      const percentage = Math.round(((basePrice - discountPrice) / basePrice) * 100);
      setDiscountPercentage(percentage);
    } else if (product.discountPercentage) {
      // Use provided discount percentage if available
      setDiscountPercentage(product.discountPercentage);
    } else if (product.offer && typeof product.offer === 'string') {
      // Try to extract percentage from offer text (e.g., "10% OFF")
      const match = product.offer.match(/(\d+)%/);
      if (match && match[1]) {
        setDiscountPercentage(parseInt(match[1]));
      }
    }
  }, [product, basePrice, discountPrice]);

  // Use discount price if available for weight options
  const effectivePrice = discountPrice || basePrice;

  // Create dynamic weight options based on product's base weight
  const createWeightOptions = () => {
    const options = [];

    // Helper function to calculate price based on weight ratio
    const calculatePriceForWeight = (targetWeight) => {
      const ratio = targetWeight / productBaseWeight;
      return {
        originalPrice: basePrice * ratio,
        price: effectivePrice * ratio,
        multiplier: ratio
      };
    };

    // Always include the product's base weight as the first option
    options.push({
      value: baseWeightLabel,
      ...calculatePriceForWeight(productBaseWeight),
      isBase: true
    });

    // Add other common weight options if they're different from base weight
    const commonWeights = [
      { weight: 500, label: '500g' },
      { weight: 1000, label: '1kg' },
      { weight: 2000, label: '2kg' }
    ];

    commonWeights.forEach(({ weight, label }) => {
      // Only add if it's different from the base weight and makes sense
      if (weight !== productBaseWeight) {
        options.push({
          value: label,
          ...calculatePriceForWeight(weight)
        });
      }
    });

    // Sort options by weight (ascending)
    return options.sort((a, b) => {
      const getWeightInGrams = (label) => {
        if (label.includes('kg')) return parseInt(label) * 1000;
        return parseInt(label);
      };
      return getWeightInGrams(a.value) - getWeightInGrams(b.value);
    });
  };

  const weightOptions = createWeightOptions();

  const handleQuantityChange = (increment) => {
    const newQuantity = quantity + increment;
    if (newQuantity >= 1 && newQuantity <= 10) {
      setQuantity(newQuantity);
    }
  };

  // Calculate the total price based on selected weight and quantity
  const calculateTotalPrice = () => {
    const selectedOption = weightOptions.find(o => o.value === selectedWeight);
    return selectedOption.price * quantity;
  };

  // Calculate the original price (before discount)
  const calculateOriginalPrice = () => {
    const selectedOption = weightOptions.find(o => o.value === selectedWeight);
    return selectedOption.originalPrice * quantity;
  };

  // Calculate savings amount
  const calculateSavings = () => {
    if (discountPrice && basePrice > discountPrice) {
      // Calculate savings based on actual discount price
      return (calculateOriginalPrice() - calculateTotalPrice()).toFixed(0);
    } else if (discountPercentage > 0) {
      // Calculate savings based on discount percentage
      const originalPrice = calculateOriginalPrice();
      return Math.round((originalPrice * discountPercentage) / 100);
    }
    return 0;
  };

  // Helper function to get selected weight option
  const getSelectedWeightOption = () => {
    return weightOptions.find(o => o.value === selectedWeight);
  };

  const handleShare = async () => {
    try {
      await Share.share({
        message: `Check out this product: ${product.name} - ${product.description}. Price: ₹${basePrice}.`,
      });
    } catch (error) {
      alert(error.message);
    }
  };

  return (
    <View className="flex-1 bg-gray-50">
      <StatusBar backgroundColor="#A31621" barStyle="light-content" />

      {/* Header */}
      <View className="bg-white">
        <View className="flex-row justify-between items-center px-4 pt-5 pb-1">
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            className="w-10 h-10 rounded-full items-center justify-center"
          >
            <MaterialIcons name="arrow-back" size={24} color="#A31621" />
          </TouchableOpacity>
          <TouchableOpacity
            className="w-10 h-10 rounded-full items-center justify-center"
            onPress={handleShare}
          >
            <MaterialIcons name="share" size={24} color="#A31621" />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView className="flex-1" showsVerticalScrollIndicator={false} contentContainerStyle={{ paddingBottom: 120 }}>
        {/* Product Image - Full Width */}
        <View className="relative">
          <Image
            source={product.image ?
              (typeof product.image === 'string' ? { uri: product.image } : product.image)
              : require('../assets/logo.png')}
            className={`w-full h-64 ${!isProductAvailable ? 'opacity-50' : ''}`}
            resizeMode="contain"
          />

          {/* Discount Badge */}
          {discountPercentage > 0 && (
            <View className="absolute top-4 right-4 bg-madder px-3 py-1 rounded-full">
              <Text className="text-white font-bold">{discountPercentage}% OFF</Text>
            </View>
          )}

          {/* Unavailable Badge */}
          {!isProductAvailable && (
            <View className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-black/70 px-4 py-2 rounded-lg">
              <Text className="text-white font-bold text-lg">Currently Unavailable</Text>
            </View>
          )}
        </View>

        {/* Product Info with Details */}
        <View className="p-5 bg-white mt-1">
          <View className="flex-row justify-between">
            <Text className="text-xl font-bold text-gray-800 w-3/4">{product.name}</Text>
          </View>

          {/* Product Description */}
          <Text className="text-gray-600 mt-2 text-base leading-6">{product.description}</Text>

          {/* Piece and Serving Info */}
          <View className="h-px bg-gray-200 mt-3" />
          <View className="mt-3 flex-row justify-center">
            <View className="flex-1 pr-2 items-center">
              <View className="flex-row items-center">
                <MaterialIcons name="restaurant" size={18} color="#4B5563" />
                <Text className="text-gray-800 ml-2 font-medium">Pieces</Text>
              </View>
              <Text className="text-gray-600 text-xs mt-1">
                {product.Pieces || "Varies by weight"}
              </Text>
            </View>
            <View className="w-px h-full bg-gray-200" />
            <View className="flex-1 pl-2 items-center">
              <View className="flex-row items-center">
                <MaterialIcons name="people" size={18} color="#4B5563" />
                <Text className="text-gray-800 ml-2 font-medium">Serving</Text>
              </View>
              <Text className="text-gray-600 text-xs mt-1">
                {product.weight ? `Serves ${Math.ceil(parseInt(product.weight) / 250)} people` : "Varies by product"}
              </Text>
            </View>
          </View>

          {/* Horizontal separator line */}
          <View className="h-px bg-gray-200 mt-3" />

          {/* Price Display with Discount */}
          <View className="flex-row items-center mt-4">
            {discountPrice && basePrice > discountPrice ? (
              // Show both original and discount price
              <View className="flex-row items-center">
                <Text className="text-xl font-bold text-madder">₹{discountPrice}</Text>
                <Text className="text-gray-500 ml-2 line-through">₹{basePrice}</Text>
                <Text className="text-gray-600 ml-2">/ {baseWeightLabel}</Text>
                <View className="ml-2 bg-red-100 px-2 py-1 rounded-lg">
                  <Text className="text-madder font-medium text-xs">{discountPercentage}% OFF</Text>
                </View>
              </View>
            ) : (
              // Show only base price
              <View className="flex-row items-center">
                <Text className="text-xl font-bold text-madder">₹{basePrice}</Text>
                <Text className="text-gray-600 ml-2">/ {baseWeightLabel}</Text>
                {discountPercentage > 0 && (
                  <View className="ml-4 bg-red-100 px-3 py-1 rounded-lg">
                    <Text className="text-madder font-medium">{discountPercentage}% OFF</Text>
                  </View>
                )}
              </View>
            )}
          </View>

          {/* Weight Selection */}
          <Text className="text-lg font-semibold text-gray-800 mt-5 mb-3">Select Weight</Text>
          <View className="flex-row flex-wrap gap-3">
            {weightOptions.map((option) => (
              <TouchableOpacity
                key={option.value}
                onPress={() => setSelectedWeight(option.value)}
                className={`px-5 py-3 rounded-xl ${selectedWeight === option.value ? 'bg-madder' : 'bg-gray-100'}`}
                disabled={!isProductAvailable}
              >
                {discountPrice && basePrice > discountPrice ? (
                  <View>
                    <Text className={`font-medium ${selectedWeight === option.value ? 'text-white' : 'text-gray-700'}`}>
                      {option.value}
                    </Text>
                    <View className="flex-row items-center">
                      <Text className={`font-medium ${selectedWeight === option.value ? 'text-white' : 'text-madder'}`}>
                        ₹{option.price.toFixed(0)}
                      </Text>
                      <Text className={`text-xs ml-1 line-through ${selectedWeight === option.value ? 'text-white/70' : 'text-gray-500'}`}>
                        ₹{option.originalPrice.toFixed(0)}
                      </Text>
                    </View>
                  </View>
                ) : (
                  <Text className={`font-medium ${selectedWeight === option.value ? 'text-white' : 'text-gray-700'}`}>
                    {option.value} - ₹{option.price.toFixed(0)}
                  </Text>
                )}
              </TouchableOpacity>
            ))}
          </View>

          {/* Quantity Selector */}
          <View className="flex-row items-center justify-between mt-6">
            <Text className="text-lg font-semibold text-gray-800">Quantity</Text>
            <View className={`flex-row items-center rounded-xl p-1 ${isProductAvailable ? 'bg-madder' : 'bg-gray-400'}`}>
              <TouchableOpacity
                onPress={() => handleQuantityChange(-1)}
                className="w-10 h-10 rounded-lg bg-snow items-center justify-center shadow-sm"
                disabled={!isProductAvailable}
              >
                <MaterialIcons name="remove" size={22} color={isProductAvailable ? "black" : "#999"} />
              </TouchableOpacity>
              <Text className="w-12 text-center font-bold text-lg color-snow">{quantity}</Text>
              <TouchableOpacity
                onPress={() => handleQuantityChange(1)}
                className="w-10 h-10 rounded-lg bg-snow items-center justify-center shadow-sm"
                disabled={!isProductAvailable}
              >
                <MaterialIcons name="add" size={22} color={isProductAvailable ? "black" : "#999"} />
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Info Cards Section */}
        <View className="mt-3 mx-3 mb-24">
          {/* Dynamic Nutritional Information */}
          <NutritionFacts product={product} selectedWeight={selectedWeight} />

          {/* Product Details Card */}
          <View className="p-4 bg-white rounded-xl shadow-sm">
            <Text className="text-lg font-semibold text-gray-800 mb-3">Product Details</Text>
            <View className="space-y-4">
              <View className="flex-row items-center">
                <View className="w-10 h-10 bg-white shadow-sm rounded-full items-center justify-center">
                  <MaterialIcons name="check-circle" size={20} color="#10B981" />
                </View>
                <View className="ml-3">
                  <Text className="text-gray-800 font-medium">Premium Quality</Text>
                  <Text className="text-gray-600 text-xs mt-1">Fresh, high-quality meat</Text>
                </View>
              </View>

              <View className="h-px bg-gray-100" />

              <View className="flex-row items-center">
                <View className="w-10 h-10 bg-white shadow-sm rounded-full items-center justify-center">
                  <MaterialIcons name="local-shipping" size={20} color="#10B981" />
                </View>
                <View className="ml-3">
                  <Text className="text-gray-800 font-medium">Free Delivery</Text>
                  <Text className="text-gray-600 text-xs mt-1">On orders above ₹499</Text>
                </View>
              </View>

              <View className="h-px bg-gray-100" />

              <View className="flex-row items-center">
                <View className="w-10 h-10 bg-white shadow-sm rounded-full items-center justify-center">
                  <MaterialIcons name="access-time" size={20} color="#10B981" />
                </View>
                <View className="ml-3">
                  <Text className="text-gray-800 font-medium">Slot Based Delivery</Text>
                  <Text className="text-gray-600 text-xs mt-1">Choose your preferred delivery slot</Text>
                </View>
              </View>

              <View className="h-px bg-gray-100" />

              <View className="flex-row items-center">
                <View className="w-10 h-10 bg-white shadow-sm rounded-full items-center justify-center">
                  <MaterialIcons name="verified" size={20} color="#10B981" />
                </View>
                <View className="ml-3">
                  <Text className="text-gray-800 font-medium">Certified</Text>
                  <Text className="text-gray-600 text-xs mt-1">FSSAI approved product</Text>
                </View>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Bottom Action Bar */}
      <View className="absolute bottom-0 left-0 right-0 bg-white p-4 border-t border-gray-200 shadow-lg">
        <View className="flex-row items-center justify-between">
          <View>
            <Text className="text-gray-600">Total Price</Text>
            {discountPrice && basePrice > discountPrice ? (
              // Show both original and discounted total price
              <View>
                <View className="flex-row items-center">
                  <Text className="text-2xl font-bold text-gray-800">
                    ₹{calculateTotalPrice().toFixed(0)}
                  </Text>
                  <Text className="text-sm text-gray-500 ml-2 line-through">
                    ₹{calculateOriginalPrice().toFixed(0)}
                  </Text>
                </View>
                <Text className="text-sm text-green-600">
                  Save ₹{calculateSavings()}
                </Text>
              </View>
            ) : (
              // Show only total price
              <View className="flex-row items-center">
                <Text className="text-2xl font-bold text-gray-800">
                  ₹{calculateTotalPrice().toFixed(0)}
                </Text>
                {discountPercentage > 0 && (
                  <Text className="text-sm text-green-600 ml-2">
                    Save ₹{calculateSavings()}
                  </Text>
                )}
              </View>
            )}
          </View>

          {isProductAvailable ? (
            // Add to Cart Button (Enabled)
            <TouchableOpacity
              className="bg-madder px-8 py-3 rounded-xl flex-row items-center"
              onPress={() => {
                // Get the selected weight option to calculate correct prices
                const selectedOption = getSelectedWeightOption();

                const cartItem = {
                  id: `${product.id}-${selectedWeight}-${Date.now()}`,
                  ...product,
                  selectedWeight,
                  quantity,
                  // Use the weight-adjusted prices
                  price: selectedOption.originalPrice, // Original price for this weight
                  discount_price: selectedOption.price !== selectedOption.originalPrice ? selectedOption.price : null,
                  // Calculate total price based on selected weight and quantity
                  totalPrice: selectedOption.price * quantity,
                  originalPrice: selectedOption.originalPrice * quantity,
                  savings: discountPrice && basePrice > discountPrice ?
                    (selectedOption.originalPrice - selectedOption.price) * quantity : 0
                };

                // Add to cart with navigation parameter
                addToCart(cartItem, navigation);
              }}
            >
              <MaterialIcons name="shopping-cart" size={24} color="white" />
              <Text className="text-white font-bold ml-2">Add to Cart</Text>
            </TouchableOpacity>
          ) : (
            // Unavailable Button (Disabled)
            <View className="bg-gray-400 px-8 py-3 rounded-xl flex-row items-center">
              <MaterialIcons name="shopping-cart" size={24} color="white" />
              <Text className="text-white font-bold ml-2">Unavailable</Text>
            </View>
          )}
        </View>
      </View>

      {/* Add to Cart Modal */}
      <AddToCartModal
        visible={addToCartModal.visible}
        onClose={closeAddToCartModal}
        onProceedToCart={proceedToCart}
        productName={addToCartModal.productName}
        productWeight={addToCartModal.productWeight}
        isUpdate={addToCartModal.isUpdate}
        isProductDetailScreen={true}
      />
    </View>
  );
};

export default ProductDetailScreen;
