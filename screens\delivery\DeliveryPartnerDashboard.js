import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { View, Text, TouchableOpacity, FlatList, Animated, Modal, TextInput, Linking, RefreshControl, Platform, Alert, Easing } from 'react-native';
import { MaterialIcons } from "@expo/vector-icons";
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import { useDeliveryPartner } from '../../context/DeliveryPartnerContext';
import { getDeliveryPartnerOrders, updateOrderStatus as apiUpdateOrderStatus } from '../../utils/api/deliveryApi';
import { refreshAccessToken } from '../../utils/tokenRefresh';
import { getUserData } from '../../utils/authStorage';
import { COLORS, SHADOWS, CARD_STYLES, BUTTON_STYLES, TEXT_STYLES, ORDER_STATUS } from '../../styles/deliveryTheme';
import { openMapsWithDirections } from '../../utils/locationUtils';
import CustomAlertModal from '../../Components/delivery/CustomAlertModal';

const DeliveryPartnerDashboard = () => {
    const navigation = useNavigation();
    const { currentDeliveryPartner, toggleAvailability, fetchDeliveryPartnerOrders } = useDeliveryPartner();

    const [activeTab, setActiveTab] = useState('pending');
    const [refreshing, setRefreshing] = useState(false);
    const [showOrderDetails, setShowOrderDetails] = useState(false);
    const [selectedOrder, setSelectedOrder] = useState(null);
    const [deliveryNote, setDeliveryNote] = useState('');
    const [showSuccessModal, setShowSuccessModal] = useState(false);
    const [successMessage, setSuccessMessage] = useState('');
    const [pendingCount, setPendingCount] = useState(0);
    const [inTransitCount, setInTransitCount] = useState(0);
    const [deliveredCount, setDeliveredCount] = useState(0);

    // Custom alert state
    const [alertVisible, setAlertVisible] = useState(false);
    const [alertTitle, setAlertTitle] = useState('');
    const [alertMessage, setAlertMessage] = useState('');
    const [alertIcon, setAlertIcon] = useState('info');
    const [alertButtons, setAlertButtons] = useState([]);

    // Animation values
    const scaleAnim = useRef(new Animated.Value(1)).current;
    const modalSlideAnim = useRef(new Animated.Value(300)).current;
    const modalFadeAnim = useRef(new Animated.Value(0)).current;

    // Orders state
    const [orders, setOrders] = useState([]);

    // Fetch orders on component mount and when currentDeliveryPartner changes
    useEffect(() => {
        let isMounted = true;
        let loadingTimeout;

        const fetchOrdersWithTimeout = async () => {
            if (!isMounted) return;

            try {
                // Set loading timeout to prevent infinite loading
                loadingTimeout = setTimeout(() => {
                    if (isMounted && refreshing) {
                        console.log('Loading timeout reached, stopping refresh');
                        setRefreshing(false);
                    }
                }, 10000); // Reduced from 15s to 10s

                if (currentDeliveryPartner) {
                    const partnerId = currentDeliveryPartner._id || currentDeliveryPartner.id;
                    console.log('Fetching orders for delivery partner:', partnerId);
                    await fetchOrders();
                } else {
                    console.log('No delivery partner available, trying to fetch from context');
                    setRefreshing(true);

                    const ordersFromContext = await fetchDeliveryPartnerOrders();
                    if (isMounted) {
                        if (ordersFromContext && ordersFromContext.length > 0) {
                            console.log('Successfully fetched orders from context:', ordersFromContext.length);
                            processOrders(ordersFromContext);
                        } else {
                            console.log('No orders returned from context');
                            setOrders([]);
                        }
                        setRefreshing(false);
                    }
                }
            } catch (error) {
                if (isMounted) {
                    console.error('Error fetching orders:', error);
                    setOrders([]);
                    setRefreshing(false);
                }
            } finally {
                if (loadingTimeout) {
                    clearTimeout(loadingTimeout);
                }
            }
        };

        fetchOrdersWithTimeout();

        // Cleanup function
        return () => {
            isMounted = false;
            if (loadingTimeout) {
                clearTimeout(loadingTimeout);
            }
        };
    }, [currentDeliveryPartner]);

    // Add a focus listener to refresh data when tab is focused
    useEffect(() => {
        const unsubscribe = navigation.addListener('focus', () => {
            console.log('Dashboard screen focused, refreshing data');
            if (currentDeliveryPartner && !refreshing) {
                fetchOrders();
            }
        });

        // Cleanup the listener on unmount
        return unsubscribe;
    }, [navigation, currentDeliveryPartner, refreshing]);

    // Calculate counts when orders change
    useEffect(() => {
        calculateCounts();
    }, [orders]);

    // Process orders from API response
    const processOrders = (ordersArray) => {
        try {
            if (!ordersArray || !Array.isArray(ordersArray)) {
                console.log('Invalid orders array:', ordersArray);
                setOrders([]);
                return;
            }

            console.log(`Processing ${ordersArray.length} orders`);

            // Format orders from API response
            const formattedOrders = ordersArray.map(order => {
                try {
                    // Extract date and time from createdAt or orderPlacedAt
                    const orderDate = new Date(order.createdAt || order.orderPlacedAt || Date.now());
                    const formattedDate = orderDate.toLocaleDateString('en-IN');
                    const formattedTime = orderDate.toLocaleTimeString('en-IN', {
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: true
                    });

                    // Map API status to UI status
                    let uiStatus = 'pending';
                    if (order.status === 'PLACED') uiStatus = 'pending';
                    if (order.status === 'CONFIRMED') uiStatus = 'pending';
                    if (order.status === 'PREPARING') uiStatus = 'pending';
                    if (order.status === 'OUT_FOR_DELIVERY') uiStatus = 'in-transit';
                    if (order.status === 'DELIVERED') uiStatus = 'delivered';
                    if (order.status === 'CANCELLED') uiStatus = 'cancelled';

                    // Also check deliveryStatus if it exists (from DeliveryPartner.orders array)
                    if (order.deliveryStatus) {
                        if (order.deliveryStatus === 'PLACED') uiStatus = 'pending';
                        if (order.deliveryStatus === 'CONFIRMED') uiStatus = 'pending';
                        if (order.deliveryStatus === 'PREPARING') uiStatus = 'pending';
                        if (order.deliveryStatus === 'OUT_FOR_DELIVERY') uiStatus = 'in-transit';
                        if (order.deliveryStatus === 'DELIVERED') uiStatus = 'delivered';
                        if (order.deliveryStatus === 'CANCELLED') uiStatus = 'cancelled';
                    }

                    // Calculate number of items
                    const itemCount = order.items ? order.items.length : 0;

                    // Get customer info from userId
                    const customerName = order.userId?.name || 'Customer';
                    const customerPhone = order.userId?.number || '';

                    // Format address - handle both string and object formats
                    let address = 'No address provided';
                    let coordinates = null;

                    // Extract coordinates from deliveryCoordinates if available
                    if (order.deliveryCoordinates &&
                        order.deliveryCoordinates.latitude &&
                        order.deliveryCoordinates.longitude) {
                        coordinates = {
                            latitude: order.deliveryCoordinates.latitude,
                            longitude: order.deliveryCoordinates.longitude
                        };
                        console.log('Found coordinates in deliveryCoordinates:', coordinates);
                    }

                    // Check for coordinates in the deliveryAddress.coordinates object
                    if (!coordinates && order.deliveryAddress &&
                        order.deliveryAddress.coordinates &&
                        order.deliveryAddress.coordinates.latitude &&
                        order.deliveryAddress.coordinates.longitude) {
                        coordinates = {
                            latitude: order.deliveryAddress.coordinates.latitude,
                            longitude: order.deliveryAddress.coordinates.longitude
                        };
                        console.log('Found coordinates in deliveryAddress.coordinates:', coordinates);
                    }

                    if (typeof order.deliveryAddress === 'string') {
                        address = order.deliveryAddress;
                    } else if (typeof order.deliveryAddress === 'object' && order.deliveryAddress !== null) {
                        // Extract coordinates from deliveryAddress if available
                        if (order.deliveryAddress.latitude && order.deliveryAddress.longitude && !coordinates) {
                            coordinates = {
                                latitude: order.deliveryAddress.latitude,
                                longitude: order.deliveryAddress.longitude
                            };
                            console.log('Found coordinates in deliveryAddress direct fields:', coordinates);
                        }

                        if (order.deliveryAddress.fullAddress) {
                            address = order.deliveryAddress.fullAddress;
                        } else {
                            // Construct from parts
                            const parts = [];
                            if (order.deliveryAddress.doorNo) parts.push(order.deliveryAddress.doorNo);
                            if (order.deliveryAddress.streetName) parts.push(order.deliveryAddress.streetName);
                            if (order.deliveryAddress.area) parts.push(order.deliveryAddress.area);
                            if (order.deliveryAddress.district) parts.push(order.deliveryAddress.district);
                            if (order.deliveryAddress.pinCode || order.deliveryAddress.pincode)
                                parts.push(order.deliveryAddress.pinCode || order.deliveryAddress.pincode);

                            address = parts.join(', ');
                        }
                    }

                    // Extract expected delivery date and time
                    let expectedDeliveryDate = '';
                    let expectedDeliveryTime = '';

                    // Extract delivered date and time if available
                    let deliveredDate = '';
                    let deliveredTime = '';
                    if (order.deliveredAt) {
                        try {
                            const deliveredDateTime = new Date(order.deliveredAt);
                            if (!isNaN(deliveredDateTime.getTime())) {
                                deliveredDate = deliveredDateTime.toLocaleDateString("en-IN", {
                                    day: "numeric", month: "short", year: "numeric"
                                });
                                deliveredTime = deliveredDateTime.toLocaleTimeString("en-IN", {
                                    hour: '2-digit', minute: '2-digit', hour12: true
                                });
                            }
                        } catch (error) {
                            console.error('Error parsing delivered date:', error);
                        }
                    }

                    // First check if we have the display info field
                    if (order.expectedDeliveryInfo) {
                        if (typeof order.expectedDeliveryInfo === 'string') {
                            expectedDeliveryDate = order.expectedDeliveryInfo;
                        } else if (typeof order.expectedDeliveryInfo === 'object') {
                            expectedDeliveryDate = order.expectedDeliveryInfo.formattedDate ||
                                (order.expectedDeliveryInfo.day === "Today"
                                    ? new Date().toLocaleDateString("en-IN", { day: "numeric", month: "short", year: "numeric" })
                                    : new Date(Date.now() + 86400000).toLocaleDateString("en-IN", { day: "numeric", month: "short", year: "numeric" }));
                            expectedDeliveryTime = order.expectedDeliveryInfo.time || '';
                        }
                    }
                    // If no display info, try to parse from the actual date field
                    else if (order.expectedDelivery) {
                        try {
                            const deliveryDate = new Date(order.expectedDelivery);
                            if (!isNaN(deliveryDate.getTime())) {
                                expectedDeliveryDate = deliveryDate.toLocaleDateString("en-IN", {
                                    day: "numeric", month: "short", year: "numeric"
                                });
                                expectedDeliveryTime = deliveryDate.toLocaleTimeString("en-IN", {
                                    hour: '2-digit', minute: '2-digit', hour12: true
                                });
                            }
                        } catch (error) {
                            console.error('Error parsing expected delivery date:', error);

                            // Fallback for legacy format
                            if (typeof order.expectedDelivery === 'string') {
                                expectedDeliveryDate = order.expectedDelivery;
                            } else if (typeof order.expectedDelivery === 'object') {
                                expectedDeliveryDate = order.expectedDelivery.formattedDate ||
                                    (order.expectedDelivery.day === "Today"
                                        ? new Date().toLocaleDateString("en-IN", { day: "numeric", month: "short", year: "numeric" })
                                        : new Date(Date.now() + 86400000).toLocaleDateString("en-IN", { day: "numeric", month: "short", year: "numeric" }));
                                expectedDeliveryTime = order.expectedDelivery.time || '';
                            }
                        }
                    }

                    // Determine if this order can be picked up
                    // An order can be picked up if:
                    // 1. It's not already assigned to any delivery partner
                    // 2. It has a status of PLACED or CONFIRMED
                    const isAssigned = !!order.deliveryPartner;
                    const canPickup = !isAssigned &&
                        ['PLACED', 'CONFIRMED'].includes(order.status) &&
                        currentDeliveryPartner?.isAvailable;

                    return {
                        id: order._id,
                        orderNumber: order.orderNumber || 'N/A',
                        customer: customerName,
                        address: address,
                        phone: customerPhone,
                        status: uiStatus,
                        items: itemCount,
                        total: order.totalAmount || 0,
                        date: formattedDate,
                        time: formattedTime,
                        distance: order.distance || '~',
                        deliveryNote: order.deliveryNote || '',
                        expectedDeliveryDate: expectedDeliveryDate,
                        expectedDeliveryTime: expectedDeliveryTime,
                        deliveredDate: deliveredDate,
                        deliveredTime: deliveredTime,
                        isAssigned: isAssigned,
                        canPickup: canPickup,
                        coordinates: coordinates, // Add coordinates for map view
                        paymentMethod: order.paymentMethod || 'Not specified',
                        // Keep original data for reference
                        originalOrder: order
                    };
                } catch (itemError) {
                    console.error('Error processing order item:', itemError, order);
                    // Return a default formatted order if there's an error
                    return {
                        id: order._id || 'unknown',
                        orderNumber: order.orderNumber || 'N/A',
                        customer: 'Error loading customer',
                        address: 'Error loading address',
                        phone: '',
                        status: 'pending',
                        items: 0,
                        total: 0,
                        date: new Date().toLocaleDateString('en-IN'),
                        time: new Date().toLocaleTimeString('en-IN'),
                        distance: '~',
                        deliveryNote: '',
                        expectedDeliveryDate: '',
                        expectedDeliveryTime: '',
                        deliveredDate: '',
                        deliveredTime: '',
                        isAssigned: false,
                        canPickup: false,
                        coordinates: null, // No coordinates for error case
                        originalOrder: order
                    };
                }
            });

            console.log(`Formatted ${formattedOrders.length} orders`);
            setOrders(formattedOrders);
        } catch (error) {
            console.error('Error processing orders:', error);
            setOrders([]);
        }
    };

    // Fetch orders from API
    const fetchOrders = async (retryCount = 0) => {
        try {
            if (!currentDeliveryPartner) {
                console.log('No delivery partner available, skipping order fetch');
                setRefreshing(false);
                return;
            }

            setRefreshing(true);
            const partnerId = currentDeliveryPartner._id || currentDeliveryPartner.id;
            console.log('Fetching orders for delivery partner...', partnerId);

            let response;
            try {
                response = await getDeliveryPartnerOrders();
            } catch (error) {
                // If 401 error, try to refresh token and retry once
                if (error.response && error.response.status === 401 && retryCount === 0) {
                    console.log('Token expired, refreshing...');
                    try {
                        await refreshAccessToken();
                        // Retry with new token
                        return await fetchOrders(1); // Prevent infinite retry
                    } catch (refreshError) {
                        console.error('Token refresh failed:', refreshError);
                        showCustomAlert(
                            "Authentication Error",
                            "Your session has expired. Please log in again.",
                            "error",
                            [{ text: "OK", onPress: () => navigation.navigate('PreLoginScreen') }]
                        );
                        setRefreshing(false);
                        return;
                    }
                } else {
                    // For other errors, show a generic error message
                    console.error('Error fetching orders:', error);
                    if (retryCount === 0) {
                        showCustomAlert(
                            "Error",
                            "Failed to fetch orders. Please try again later.",
                            "error",
                            [{ text: "Retry", onPress: () => fetchOrders(1) }]
                        );
                    }
                    setRefreshing(false);
                    return;
                }
            }

            // Add additional validation for response
            if (!response) {
                console.log('No orders received from server, setting empty array');
                setOrders([]);
                setRefreshing(false);
                return;
            }

            console.log(`Received ${response?.length || 0} orders from API`);

            let ordersArray = response;

            // Handle different response formats
            if (!Array.isArray(response)) {
                console.log('Response is not an array, trying to extract orders array');
                // Try to extract orders array if response is an object
                if (response?.orders && Array.isArray(response.orders)) {
                    console.log('Found orders array in response.orders');
                    ordersArray = response.orders;
                } else if (response?.data?.orders && Array.isArray(response.data.orders)) {
                    console.log('Found orders array in response.data.orders');
                    ordersArray = response.data.orders;
                } else {
                    console.log('Could not find orders array in response, setting empty array');
                    setOrders([]);
                    setRefreshing(false);
                    return;
                }
            }

            // Log the total number of orders
            console.log(`Total orders available: ${ordersArray.length}`);

            // Log a sample order structure for debugging
            if (ordersArray.length > 0) {
                console.log(`Sample order structure: ${JSON.stringify(ordersArray[0], null, 2).substring(0, 200)}...`);
            }

            // Filter orders for the current delivery partner
            const filteredOrders = ordersArray.filter(order => {
                try {
                    // Check if the order has a deliveryPartner field
                    if (!order.deliveryPartner) {
                        // Also check for other fields that might contain the delivery partner ID
                        const alternativeFields = [
                            'deliveryPartnerId', 'deliveryPartnerID',
                            'delivery_partner_id', 'assignedTo', 'assignedDeliveryPartner'
                        ];

                        for (const field of alternativeFields) {
                            if (order[field]) {
                                const fieldValue = order[field];
                                const fieldValueStr = String(typeof fieldValue === 'object' ?
                                    (fieldValue._id || fieldValue.id) : fieldValue);
                                const currentIdStr = String(partnerId);

                                if (fieldValueStr === currentIdStr) {
                                    return true;
                                }
                            }
                        }

                        // Check for timestamps in the order that match the delivery partner
                        if (order.deliveryPartnerAssignedAt &&
                            order.deliveryStartedAt &&
                            order.deliveredAt) {
                            console.log('Order has delivery timestamps, might belong to this partner');
                            return true;
                        }

                        return false;
                    }

                    // Handle different formats of deliveryPartner field
                    const orderDeliveryPartnerId =
                        typeof order.deliveryPartner === 'object'
                            ? (order.deliveryPartner._id || order.deliveryPartner.id)
                            : order.deliveryPartner;

                    // Convert both to strings for comparison
                    const orderIdStr = String(orderDeliveryPartnerId);
                    const currentIdStr = String(partnerId);

                    // Direct string comparison
                    if (orderIdStr === currentIdStr) {
                        return true;
                    }

                    // Special handling for MongoDB ObjectId format
                    // Check if both are MongoDB ObjectIds (24 hex characters)
                    const isOrderIdObjectId = /^[0-9a-f]{24}$/i.test(orderIdStr);
                    const isCurrentIdObjectId = /^[0-9a-f]{24}$/i.test(currentIdStr);

                    if (isOrderIdObjectId && isCurrentIdObjectId) {
                        // For MongoDB ObjectIds, we need to compare them as strings
                        return orderIdStr === currentIdStr;
                    }

                    // Special handling for custom ID format (e.g., DP002)
                    if (typeof currentIdStr === 'string' && currentIdStr.startsWith('DP')) {
                        // Check if the order's deliveryPartner is an object with an id property
                        if (typeof order.deliveryPartner === 'object' &&
                            order.deliveryPartner.id &&
                            String(order.deliveryPartner.id) === currentIdStr) {
                            return true;
                        }

                        // Check other possible fields
                        const fieldsToCheck = [
                            'deliveryPartnerId', 'deliveryPartnerID',
                            'delivery_partner_id', 'assignedTo', 'assignedDeliveryPartner'
                        ];

                        for (const field of fieldsToCheck) {
                            if (order[field] && String(order[field]) === currentIdStr) {
                                return true;
                            }
                        }
                    }

                    // Check for timestamps in the order that match the delivery partner
                    // This is a fallback for when the IDs don't match but we have timestamps
                    if (order.deliveryPartnerAssignedAt &&
                        order.deliveryStartedAt &&
                        order.deliveredAt) {
                        // If the order has all the delivery timestamps, it's likely assigned to this partner
                        console.log('Order has delivery timestamps, checking if it belongs to this partner');
                        return true;
                    }

                    return false;
                } catch (error) {
                    console.error('Error filtering order:', error);
                    return false;
                }
            });

            console.log(`Found ${filteredOrders.length} orders for delivery partner ${partnerId}`);

            // Always check for unassigned orders that can be picked up if the delivery partner is available
            if (currentDeliveryPartner?.isAvailable) {
                console.log('Checking for unassigned orders');

                // Filter for unassigned orders (no deliveryPartner) with status PLACED or CONFIRMED
                const unassignedOrders = ordersArray.filter(order =>
                    !order.deliveryPartner &&
                    (order.status === 'PLACED' || order.status === 'CONFIRMED')
                );

                console.log(`Found ${unassignedOrders.length} unassigned orders that can be picked up`);

                // Process both assigned and unassigned orders
                processOrders([...filteredOrders, ...unassignedOrders]);
            } else {
                // Process only the filtered orders if delivery partner is not available
                processOrders(filteredOrders);
            }
        } catch (error) {
            console.error('Error fetching orders:', error);

            // Handle specific error cases
            if (error.response?.status === 401 || error.response?.status === 403) {
                showCustomAlert(
                    "Authentication Error",
                    "Please log in again to continue.",
                    "error",
                    [{ text: "OK", style: "default", onPress: () => navigation.navigate('Login') }]
                );
                return;
            }

            // Retry logic (up to 1 retry to prevent infinite loops)
            if (retryCount < 1) {
                const delay = 2000; // Fixed 2 second delay
                console.log(`Retrying fetch orders (attempt ${retryCount + 1}) after ${delay}ms...`);
                setTimeout(() => fetchOrders(retryCount + 1), delay);
                return;
            }

            // Show error alert only after retries fail
            showCustomAlert(
                "Error",
                "Failed to fetch orders. Please check your connection and try again.",
                "error",
                [{ text: "Retry", style: "default", onPress: () => fetchOrders(0) }]
            );
        } finally {
            setRefreshing(false);
        }
    };

    const calculateCounts = useCallback(() => {
        const pending = orders.filter(order => order.status === 'pending').length;
        const inTransit = orders.filter(order => order.status === 'in-transit').length;
        const delivered = orders.filter(order => order.status === 'delivered').length;

        setPendingCount(pending);
        setInTransitCount(inTransit);
        setDeliveredCount(delivered);
    }, [orders]);

    // Filter orders based on active tab - memoized for performance
    const filteredOrders = useMemo(() => {
        return orders.filter(order => {
            if (activeTab === 'pending') return order.status === 'pending';
            if (activeTab === 'in-transit') return order.status === 'in-transit';
            if (activeTab === 'delivered') return order.status === 'delivered';
            return true;
        });
    }, [orders, activeTab]);

    const onRefresh = useCallback(() => {
        if (!refreshing) {
            fetchOrders();
        }
    }, [refreshing]);

    const handleStatusChange = (orderId, newStatus) => {
        if (newStatus === 'delivered') {
            const order = orders.find(order => order.id === orderId);
            setSelectedOrder(order);
            setDeliveryNote('');

            // Reset animation values
            modalSlideAnim.setValue(300);
            modalFadeAnim.setValue(0);

            // Show the modal
            setShowOrderDetails(true);

            // Animate the modal in
            Animated.parallel([
                Animated.timing(modalSlideAnim, {
                    toValue: 0,
                    duration: 300,
                    useNativeDriver: true,
                    easing: Easing.out(Easing.cubic)
                }),
                Animated.timing(modalFadeAnim, {
                    toValue: 1,
                    duration: 250,
                    useNativeDriver: true
                })
            ]).start();
        } else {
            updateOrderStatus(orderId, newStatus);
        }
    };

    const updateOrderStatus = async (orderId, newStatus, note = '') => {
        try {
            // Convert UI status to API status
            let apiStatus = 'PLACED';
            if (newStatus === 'in-transit') apiStatus = 'OUT_FOR_DELIVERY';
            if (newStatus === 'delivered') apiStatus = 'DELIVERED';
            if (newStatus === 'cancelled') apiStatus = 'CANCELLED';

            console.log(`Converting UI status ${newStatus} to API status ${apiStatus}`);

            // Call API to update status
            await apiUpdateOrderStatus(orderId, apiStatus, note);

            // Update local state
            setOrders(prevOrders =>
                prevOrders.map(order =>
                    order.id === orderId
                        ? { ...order, status: newStatus, deliveryNote: note || order.deliveryNote }
                        : order
                )
            );

            // Show success message
            if (newStatus === 'delivered') {
                setSuccessMessage('Order delivered successfully!');
                setShowSuccessModal(true);
                setTimeout(() => setShowSuccessModal(false), 2000);
            } else if (newStatus === 'in-transit') {
                setSuccessMessage('Order pickup confirmed!');
                setShowSuccessModal(true);
                setTimeout(() => setShowSuccessModal(false), 2000);
            }

            // Refresh orders after a short delay
            setTimeout(() => {
                fetchOrders();
            }, 2000);
        } catch (error) {
            console.error('Error updating order status:', error);

            // Special handling for 500 errors - these might actually be successful
            // but the server is returning an error
            if (error.response && error.response.status === 500) {
                console.log('Server returned 500 error, but the status might have been updated. Refreshing orders list...');

                // Refresh the orders list to check if the order status was actually updated
                await fetchOrders();

                // Check if the order status has been updated
                const updatedOrder = orders.find(o => o.id === orderId);
                if (updatedOrder && updatedOrder.status === newStatus) {
                    console.log('Order status appears to be updated despite 500 error. Showing success message.');

                    // Show success message based on the status
                    if (newStatus === 'delivered') {
                        setSuccessMessage('Order delivered successfully!');
                        setShowSuccessModal(true);
                        setTimeout(() => setShowSuccessModal(false), 2000);
                    } else if (newStatus === 'in-transit') {
                        setSuccessMessage('Order pickup confirmed!');
                        setShowSuccessModal(true);
                        setTimeout(() => setShowSuccessModal(false), 2000);
                    }
                    return;
                }
            }

            // Show error message with more details
            let errorMessage = "Failed to update order status. Please try again.";

            if (error.response) {
                if (error.response.status === 403) {
                    errorMessage = "You are not authorized to update this order. It may be assigned to another delivery partner.";
                } else if (error.response.status === 500) {
                    // For 500 errors, give a more helpful message
                    errorMessage = "The server encountered an error. Please refresh the orders list to check if your order status was updated.";
                } else if (error.response.data && error.response.data.message) {
                    errorMessage = error.response.data.message;
                }
            }

            showCustomAlert(
                "Error",
                errorMessage,
                "error",
                [{
                    text: "Refresh Orders",
                    style: "default",
                    onPress: () => fetchOrders()
                }]
            );
        }
    };

    const handleCall = (phoneNumber, customerName = 'Customer') => {
        if (!phoneNumber) {
            showCustomAlert(
                "No Phone Number",
                "No phone number available for this customer",
                "phone-disabled",
                [{ text: "OK", style: 'default' }]
            );
            return;
        }

        // Format phone number for display in Indian format (+91 XXXXXXXXXX)
        let formattedPhone = phoneNumber;

        // Remove any non-digit characters
        const digitsOnly = phoneNumber.replace(/\D/g, '');

        // If it's a 10-digit number, format as +91 XXXXXXXXXX
        if (digitsOnly.length === 10) {
            formattedPhone = `+91 ${digitsOnly}`;
        }
        // If it already has country code (assuming 91 for India)
        else if (digitsOnly.length > 10 && digitsOnly.startsWith('91')) {
            formattedPhone = `+${digitsOnly.substring(0, 2)} ${digitsOnly.substring(2)}`;
        }

        showCustomAlert(
            "Call Customer",
            `Would you like to call ${customerName}?\n\n${formattedPhone}`,
            "call",
            [
                {
                    text: "Cancel",
                    style: "cancel"
                },
                {
                    text: "Call",
                    style: 'default',
                    onPress: () => {
                        const phoneUrl = `tel:${phoneNumber.replace(/\s/g, '')}`;
                        Linking.canOpenURL(phoneUrl)
                            .then(supported => {
                                if (supported) {
                                    return Linking.openURL(phoneUrl);
                                } else {
                                    showCustomAlert(
                                        "Device Error",
                                        "Phone calls are not supported on this device",
                                        "error",
                                        [{ text: "OK", style: 'default' }]
                                    );
                                }
                            })
                            .catch(err => {
                                console.error('Error opening phone app:', err);
                                showCustomAlert(
                                    "Error",
                                    "Could not open phone application",
                                    "error",
                                    [{ text: "OK", style: 'default' }]
                                );
                            });
                    }
                }
            ]
        );
    };

    // Helper function to show custom alert
    const showCustomAlert = (title, message, icon = 'info', buttons = [{ text: "OK", style: "default" }]) => {
        setAlertTitle(title);
        setAlertMessage(message);
        setAlertIcon(icon);
        setAlertButtons(buttons);
        setAlertVisible(true);
    };

    const handleMap = (item) => {
        if (!item || (!item.address && (!item.coordinates || !item.coordinates.latitude || !item.coordinates.longitude))) {
            showCustomAlert(
                "No Location Data",
                "No address or coordinates available for this delivery",
                "location-off",
                [{ text: "OK", style: "default" }]
            );
            return;
        }

        // Format address for display (truncate if too long)
        const displayAddress = item.address && item.address.length > 60
            ? item.address.substring(0, 57) + '...'
            : (item.address || "Location coordinates available");

        // Log the item data for debugging
        console.log('Map navigation requested for item:', {
            id: item.id,
            orderNumber: item.orderNumber,
            hasCoordinates: !!(item.coordinates && item.coordinates.latitude && item.coordinates.longitude),
            coordinates: item.coordinates,
            address: item.address
        });

        // Check if we have coordinates
        if (item.coordinates && item.coordinates.latitude && item.coordinates.longitude) {
            // Show confirmation with coordinates-based navigation option
            showCustomAlert(
                "Open Maps",
                `Would you like to get directions to this address?\n\n${displayAddress}`,
                "directions",
                [
                    {
                        text: "Cancel",
                        style: "cancel"
                    },
                    {
                        text: "Open in Maps",
                        style: "default",
                        onPress: () => {
                            // Use coordinates for more precise navigation
                            const { latitude, longitude } = item.coordinates;
                            console.log('Opening maps with coordinates:', latitude, longitude);

                            // Use the utility function for consistent behavior
                            openMapsWithDirections(item.address, item.coordinates, true);
                        }
                    }
                ]
            );
        } else {
            // No coordinates available, use address string
            showCustomAlert(
                "Open Maps",
                `Would you like to get directions to this address?\n\n${displayAddress}`,
                "directions",
                [
                    {
                        text: "Cancel",
                        style: "cancel"
                    },
                    {
                        text: "Open Maps",
                        style: "default",
                        onPress: () => {
                            // Use the utility function for consistent behavior
                            openMapsWithDirections(item.address, null, true);
                        }
                    }
                ]
            );
        }
    };

    const handleToggleAvailability = async () => {
        // Check if currentDeliveryPartner exists
        if (!currentDeliveryPartner) {
            showCustomAlert(
                "Error",
                "Delivery partner information not available. Please try again later.",
                "error",
                [{ text: "OK", style: "default" }]
            );
            return;
        }

        // If currently available and trying to go unavailable while having in-transit orders
        const inTransitOrderCount = inTransitCount > 0 ? inTransitCount : (currentDeliveryPartner.inTransitOrders || 0);

        if (currentDeliveryPartner.isAvailable && inTransitOrderCount > 0) {
            showCustomAlert(
                "Cannot Change Status",
                `You have ${inTransitOrderCount} order(s) in transit. Please complete all deliveries before going offline.`,
                "warning",
                [{
                    text: "OK",
                    style: "default"
                }]
            );
            return;
        }

        try {
            // Show loading state
            setRefreshing(true);

            // Get the current availability status
            const isCurrentlyAvailable = currentDeliveryPartner.isAvailable === true;
            console.log('Current availability status:', isCurrentlyAvailable);
            console.log('Setting availability to:', !isCurrentlyAvailable);

            // Animate the status indicator immediately for better UX
            Animated.sequence([
                Animated.timing(scaleAnim, {
                    toValue: 1.2,
                    duration: 200,
                    useNativeDriver: true
                }),
                Animated.timing(scaleAnim, {
                    toValue: 1,
                    duration: 200,
                    useNativeDriver: true
                })
            ]).start();

            // Call the API to update the status
            // This will also update the context state
            await toggleAvailability(currentDeliveryPartner.id || currentDeliveryPartner._id);

            // Refresh orders to ensure UI and backend are in sync
            await fetchOrders();

            // The availability is already updated in the context via toggleAvailability
            const newAvailability = !isCurrentlyAvailable;

            // Show success message
            const newStatus = newAvailability ? "Available" : "Unavailable";
            setSuccessMessage(`You are now ${newStatus} for deliveries`);
            setShowSuccessModal(true);
            setTimeout(() => setShowSuccessModal(false), 2000);

        } catch (error) {
            console.error('Error toggling availability:', error);

            // Log detailed error information
            if (error.response) {
                console.error('Error response:', {
                    status: error.response.status,
                    data: error.response.data
                });
            }

            showCustomAlert(
                "Error",
                "Failed to update availability status. Please try again.",
                "error",
                [{ text: "OK", style: "default" }]
            );
        } finally {
            // End loading state
            setRefreshing(false);
        }
    };

    const handleCompleteDelivery = () => {
        if (selectedOrder) {
            updateOrderStatus(selectedOrder.id, 'delivered', deliveryNote);
            setShowOrderDetails(false);
        }
    };

    // Function to handle order pickup
    const handlePickupOrder = async (orderId) => {
        try {
            // Check if delivery partner is available
            if (!currentDeliveryPartner?.isAvailable) {
                showCustomAlert(
                    "Not Available",
                    "You need to set your status to Available before picking up orders.",
                    "info",
                    [
                        {
                            text: "Cancel",
                            style: "cancel"
                        },
                        {
                            text: "Set Available",
                            style: "default",
                            onPress: async () => {
                                try {
                                    await handleToggleAvailability();
                                    // Refresh orders after changing availability
                                    fetchOrders();
                                } catch (error) {
                                    console.error('Error updating availability:', error);
                                    showCustomAlert("Error", "Failed to update availability status.", "error", [{ text: "OK", style: "default" }]);
                                }
                            }
                        }
                    ]
                );
                return;
            }

            setRefreshing(true);
            console.log(`Requesting to pick up order: ${orderId}`);

            try {
                // Call the API to assign the order to this delivery partner
                const response = await apiUpdateOrderStatus(orderId, null, '', true);
                console.log('Order pickup response:', JSON.stringify(response, null, 2));

                // Refresh the orders list
                fetchOrders();

                // Show success message
                setSuccessMessage('Order has been assigned to you. You can now start the delivery.');
                setShowSuccessModal(true);
                setTimeout(() => setShowSuccessModal(false), 2000);
            } catch (error) {
                // Check if the error is because the order is already assigned
                if (error.response && error.response.status === 400) {
                    // Check if the error message indicates the order is already assigned
                    const errorMessage = error.response.data?.message || '';
                    if (errorMessage.includes('already assigned')) {
                        console.log('Order is already assigned, refreshing orders list');

                        // Refresh the orders list to get the latest status
                        fetchOrders();

                        // Show a more helpful message
                        setSuccessMessage('This order has already been assigned. The orders list has been refreshed.');
                        setShowSuccessModal(true);
                        setTimeout(() => setShowSuccessModal(false), 2000);
                        return;
                    }
                }

                // Special handling for 500 errors - these might actually be successful
                // but the server is returning an error
                if (error.response && error.response.status === 500) {
                    console.log('Server returned 500 error, but the order might have been assigned. Refreshing orders list...');

                    // Refresh the orders list to check if the order was actually assigned
                    await fetchOrders();

                    // Check if the order is now assigned to this delivery partner
                    const updatedOrder = orders.find(o => o.id === orderId);
                    if (updatedOrder && updatedOrder.isAssigned) {
                        console.log('Order appears to be assigned despite 500 error. Showing success message.');

                        // Show success message
                        setSuccessMessage('Order has been assigned to you. You can now start the delivery.');
                        setShowSuccessModal(true);
                        setTimeout(() => setShowSuccessModal(false), 2000);
                        return;
                    }
                }

                // Re-throw for other errors to be caught by the outer catch block
                throw error;
            }
        } catch (error) {
            console.error('Error picking up order:', error);

            // Show error message with more details
            let errorMessage = "Failed to pick up order. Please try again.";

            if (error.response) {
                if (error.response.status === 403) {
                    errorMessage = "You are not authorized to pick up this order. It may be assigned to another delivery partner.";
                } else if (error.response.status === 500) {
                    // For 500 errors, give a more helpful message
                    errorMessage = "The server encountered an error. Please refresh the orders list to check if your order was assigned.";
                } else if (error.response.data && error.response.data.message) {
                    errorMessage = error.response.data.message;
                }
            }

            showCustomAlert(
                "Error",
                errorMessage,
                "error",
                [{
                    text: "Refresh Orders",
                    style: "default",
                    onPress: () => fetchOrders()
                }]
            );
        } finally {
            setRefreshing(false);
        }
    };

    const renderOrderItem = ({ item }) => {
        const statusConfig = ORDER_STATUS[item.status] || ORDER_STATUS.pending;

        return (
            <TouchableOpacity
                className="bg-white rounded-xl p-4 mb-4 shadow-sm"
                style={SHADOWS.small}
                onPress={() => {
                    setSelectedOrder(item);

                    // Reset animation values
                    modalSlideAnim.setValue(300);
                    modalFadeAnim.setValue(0);

                    // Show the modal
                    setShowOrderDetails(true);

                    // Animate the modal in
                    Animated.parallel([
                        Animated.timing(modalSlideAnim, {
                            toValue: 0,
                            duration: 300,
                            useNativeDriver: true,
                            easing: Easing.out(Easing.cubic)
                        }),
                        Animated.timing(modalFadeAnim, {
                            toValue: 1,
                            duration: 250,
                            useNativeDriver: true
                        })
                    ]).start();
                }}
                activeOpacity={0.7}
            >
                {/* Order header with status badge */}
                <View className="flex-row justify-between items-start mb-3">
                    <View className="flex-1 mr-2">
                        <View className="flex-row items-center">
                            <View className="bg-madder/10 p-1.5 rounded-lg mr-2">
                                <MaterialIcons name="receipt" size={18} color={COLORS.primary} />
                            </View>
                            <View>
                                <Text className="text-lg font-bold text-gray-800">Order #{item.orderNumber}</Text>
                                <View className="flex-row items-center mt-0.5">
                                    <MaterialIcons name="event" size={14} color={COLORS.darkGray} />
                                    <Text className="text-gray-500 ml-1 text-xs">{item.date} • {item.time}</Text>
                                </View>
                            </View>
                        </View>

                        {/* Available badge moved below order number */}
                        {item.canPickup && (
                            <View className="mt-2 bg-green-500 px-2 py-0.5 rounded-full self-start">
                                <Text className="text-white text-xs font-bold">AVAILABLE</Text>
                            </View>
                        )}
                    </View>

                    <View className="flex-row items-center">
                        <View
                            className="px-3 py-1.5 rounded-full mr-2"
                            style={{ backgroundColor: statusConfig.bgColor }}
                        >
                            <View className="flex-row items-center">
                                <MaterialIcons name={statusConfig.icon} size={14} color={statusConfig.color} />
                                <Text
                                    className="text-xs font-medium ml-1"
                                    style={{ color: statusConfig.textColor }}
                                >
                                    {statusConfig.label}
                                </Text>
                            </View>
                        </View>
                    </View>
                </View>

                {/* Expected Delivery Information - Highlighted */}
                {(item.expectedDeliveryDate || item.expectedDeliveryTime) && (
                    <View className="mb-3">
                        <LinearGradient
                            colors={[COLORS.primary, COLORS.primaryLight]}
                            start={{ x: 0, y: 0 }}
                            end={{ x: 1, y: 0 }}
                            className="p-2.5 rounded-lg"
                        >
                            <View className="flex-row items-center">
                                <View className="bg-white/20 p-1 rounded-md">
                                    <MaterialIcons name="schedule" size={16} color="#FFFFFF" />
                                </View>
                                <View className="ml-2">
                                    <Text className="text-xs text-white/80 font-medium">EXPECTED DELIVERY</Text>
                                    <Text className="text-sm font-bold text-white">
                                        {item.expectedDeliveryDate}
                                        {item.expectedDeliveryTime ? ` • ${item.expectedDeliveryTime}` : ''}
                                    </Text>
                                </View>
                            </View>
                        </LinearGradient>
                    </View>
                )}

                {/* Delivered Time Information */}
                {item.status === 'delivered' && (item.deliveredDate || item.deliveredTime) && (
                    <View className="mb-3">
                        <LinearGradient
                            colors={['#16A34A', '#22C55E']}
                            start={{ x: 0, y: 0 }}
                            end={{ x: 1, y: 0 }}
                            className="p-2.5 rounded-lg"
                        >
                            <View className="flex-row items-center">
                                <View className="bg-white/20 p-1 rounded-md">
                                    <MaterialIcons name="check-circle" size={16} color="#FFFFFF" />
                                </View>
                                <View className="ml-2">
                                    <Text className="text-xs text-white/80 font-medium">DELIVERED ON</Text>
                                    <Text className="text-sm font-bold text-white">
                                        {item.deliveredDate}
                                        {item.deliveredTime ? ` • ${item.deliveredTime}` : ''}
                                    </Text>
                                </View>
                            </View>
                        </LinearGradient>
                    </View>
                )}

                {/* Customer and address info */}
                <View className="bg-gray-50 p-3 rounded-lg mb-3">
                    {/* Customer info with call button */}
                    <View className="flex-row items-start mb-3">
                        <View className="bg-madder/10 p-1 rounded-md mt-0.5">
                            <MaterialIcons name="person" size={16} color={COLORS.primary} />
                        </View>
                        <View className="ml-2 flex-1">
                            <Text className="text-xs text-madder font-bold">CUSTOMER</Text>
                            <Text className="font-bold text-gray-800">{item.customer}</Text>
                        </View>
                        <TouchableOpacity
                            className="bg-green-100 p-2 rounded-lg ml-2"
                            onPress={() => handleCall(item.phone, item.customer)}
                            accessibilityLabel={`Call ${item.customer}`}
                            accessibilityHint="Tap to call the customer"
                        >
                            <MaterialIcons name="call" size={20} color="#16A34A" />
                        </TouchableOpacity>
                    </View>

                    {/* Delivery address with directions button */}
                    <View className="flex-row items-start mb-3">
                        <View className="bg-madder/10 p-1 rounded-md mt-0.5">
                            <MaterialIcons name="location-on" size={16} color={COLORS.primary} />
                        </View>
                        <View className="ml-2 flex-1">
                            <Text className="text-xs text-madder font-bold">DELIVERY ADDRESS</Text>
                            <Text className="text-gray-700">{item.address}</Text>
                        </View>
                        <TouchableOpacity
                            className="bg-blue-100 p-2 rounded-lg ml-2"
                            onPress={() => handleMap(item)}
                            accessibilityLabel="Get directions"
                            accessibilityHint="Tap to open maps with directions to delivery address"
                        >
                            <MaterialIcons name="directions" size={20} color="#2563EB" />
                        </TouchableOpacity>
                    </View>

                    <View className="flex-row justify-between pt-3 border-t border-gray-200">
                        <View className="flex-row items-center">
                            <MaterialIcons name="shopping-bag" size={16} color={COLORS.darkGray} />
                            <Text className="text-gray-600 ml-1 font-medium">{item.items} items</Text>
                        </View>

                        <View className="bg-madder/10 px-3 py-1 rounded-lg">
                            <Text className="text-madder font-bold">₹{item.total}</Text>
                        </View>
                    </View>
                </View>

                {/* Action buttons */}
                <View className="flex-row justify-between items-center">
                    {item.canPickup && (
                        <TouchableOpacity
                            className="bg-madder w-full px-4 py-3 rounded-lg flex-row justify-center items-center"
                            onPress={() => handlePickupOrder(item.id)}
                        >
                            <MaterialIcons name="assignment" size={18} color="white" />
                            <Text className="text-white font-medium ml-2">Pick Up Order</Text>
                        </TouchableOpacity>
                    )}
                    {item.status === 'pending' && item.isAssigned && (
                        <TouchableOpacity
                            className="bg-madder w-full px-4 py-3 rounded-lg flex-row justify-center items-center"
                            onPress={() => handleStatusChange(item.id, 'in-transit')}
                        >
                            <MaterialIcons name="local-shipping" size={18} color="white" />
                            <Text className="text-white font-medium ml-2">Start Delivery</Text>
                        </TouchableOpacity>
                    )}

                    {item.status === 'in-transit' && (
                        <TouchableOpacity
                            className="bg-green-600 w-full px-4 py-3 rounded-lg flex-row justify-center items-center"
                            onPress={() => handleStatusChange(item.id, 'delivered')}
                        >
                            <MaterialIcons name="check-circle" size={18} color="white" />
                            <Text className="text-white font-medium ml-2">Mark Delivered</Text>
                        </TouchableOpacity>
                    )}

                    {item.status === 'delivered' && (
                        <View className="bg-gray-100 w-full px-4 py-3 rounded-lg flex-row justify-center items-center">
                            <MaterialIcons name="verified" size={18} color={COLORS.success} />
                            <Text className="text-gray-700 font-medium ml-2">Completed</Text>
                        </View>
                    )}
                </View>
            </TouchableOpacity>
        );
    };

    return (
        <View className="flex-1 bg-snow">
            <LinearGradient
                colors={[COLORS.primaryDark, COLORS.primary]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                className="p-4 pt-14 pb-6 rounded-b-3xl"
                style={SHADOWS.medium}
            >
                <View className="flex-row justify-between items-center mb-3">
                    <View className="flex-row items-center">
                        <View className="bg-white/20 p-2 rounded-xl mr-2">
                            <MaterialIcons name="dashboard" size={24} color="white" />
                        </View>
                        <View>
                            <Text className="text-xs text-white/80 font-medium">Welcome back</Text>
                            <Text className="text-xl text-white font-bold">Delivery Dashboard</Text>
                        </View>
                    </View>

                    <TouchableOpacity
                        className={`flex-row items-center ${currentDeliveryPartner && currentDeliveryPartner.isAvailable ? 'bg-green-500/90' : 'bg-gray-500/80'} px-4 py-2 rounded-full`}
                        style={SHADOWS.small}
                        onPress={handleToggleAvailability}
                    >
                        <Animated.View
                            className={`w-3 h-3 rounded-full mr-2 ${currentDeliveryPartner && currentDeliveryPartner.isAvailable ? 'bg-white' : 'bg-gray-300'}`}
                            style={{ transform: [{ scale: scaleAnim }] }}
                        />
                        <Text className="text-white font-medium">
                            {currentDeliveryPartner && currentDeliveryPartner.isAvailable === true ? 'Available' : 'Unavailable'}
                        </Text>
                    </TouchableOpacity>
                </View>


            </LinearGradient>

            <View className="mx-4 mt-4">
                <Text className="text-lg font-bold text-gray-800 mb-3">My Orders</Text>
                <View className="flex-row bg-white rounded-xl overflow-hidden shadow-sm">
                    <TouchableOpacity
                        className={`flex-1 py-2 px-1`}
                        onPress={() => setActiveTab('pending')}
                    >
                        <View className={`items-center py-2 px-2 rounded-lg ${activeTab === 'pending' ? 'bg-madder/10' : 'bg-white'}`}>
                            <View className="flex-row items-center justify-center">
                                <MaterialIcons
                                    name="pending-actions"
                                    size={20}
                                    color={activeTab === 'pending' ? COLORS.primary : COLORS.darkGray}
                                />
                                <Text
                                    className={`text-center font-medium ml-1.5 ${activeTab === 'pending' ? 'text-madder' : 'text-gray-600'}`}
                                >
                                    Pending
                                </Text>
                            </View>
                            <View className={`rounded-full px-2 py-0.5 mt-1 ${activeTab === 'pending' ? 'bg-madder' : 'bg-gray-200'}`}>
                                <Text className={`text-xs font-bold ${activeTab === 'pending' ? 'text-white' : 'text-gray-600'}`}>
                                    {pendingCount}
                                </Text>
                            </View>
                            {activeTab === 'pending' && (
                                <View className="h-1 w-full bg-madder rounded-full mt-1" />
                            )}
                        </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                        className={`flex-1 py-2 px-1`}
                        onPress={() => setActiveTab('in-transit')}
                    >
                        <View className={`items-center py-2 px-2 rounded-lg ${activeTab === 'in-transit' ? 'bg-madder/10' : 'bg-white'}`}>
                            <View className="flex-row items-center justify-center">
                                <MaterialIcons
                                    name="local-shipping"
                                    size={20}
                                    color={activeTab === 'in-transit' ? COLORS.primary : COLORS.darkGray}
                                />
                                <Text
                                    className={`text-center font-medium ml-1.5 ${activeTab === 'in-transit' ? 'text-madder' : 'text-gray-600'}`}
                                >
                                    In Transit
                                </Text>
                            </View>
                            <View className={`rounded-full px-2 py-0.5 mt-1 ${activeTab === 'in-transit' ? 'bg-madder' : 'bg-gray-200'}`}>
                                <Text className={`text-xs font-bold ${activeTab === 'in-transit' ? 'text-white' : 'text-gray-600'}`}>
                                    {inTransitCount}
                                </Text>
                            </View>
                            {activeTab === 'in-transit' && (
                                <View className="h-1 w-full bg-madder rounded-full mt-1" />
                            )}
                        </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                        className={`flex-1 py-2 px-1`}
                        onPress={() => setActiveTab('delivered')}
                    >
                        <View className={`items-center py-2 px-2 rounded-lg ${activeTab === 'delivered' ? 'bg-madder/10' : 'bg-white'}`}>
                            <View className="flex-row items-center justify-center">
                                <MaterialIcons
                                    name="check-circle"
                                    size={20}
                                    color={activeTab === 'delivered' ? COLORS.primary : COLORS.darkGray}
                                />
                                <Text
                                    className={`text-center font-medium ml-1.5 ${activeTab === 'delivered' ? 'text-madder' : 'text-gray-600'}`}
                                >
                                    Delivered
                                </Text>
                            </View>
                            <View className={`rounded-full px-2 py-0.5 mt-1 ${activeTab === 'delivered' ? 'bg-madder' : 'bg-gray-200'}`}>
                                <Text className={`text-xs font-bold ${activeTab === 'delivered' ? 'text-white' : 'text-gray-600'}`}>
                                    {deliveredCount}
                                </Text>
                            </View>
                            {activeTab === 'delivered' && (
                                <View className="h-1 w-full bg-madder rounded-full mt-1" />
                            )}
                        </View>
                    </TouchableOpacity>
                </View>
            </View>

            <FlatList
                className="p-4 pt-2"
                data={filteredOrders}
                keyExtractor={item => item.id}
                renderItem={renderOrderItem}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{ paddingBottom: 100 }}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={onRefresh}
                        colors={['#A31621']}
                        tintColor="#A31621"
                    />
                }
                ListEmptyComponent={
                    <View className="items-center justify-center py-10 px-6">
                        <View className="bg-gray-100 p-4 rounded-full mb-4">
                            <MaterialIcons name="inbox" size={48} color={COLORS.primary} />
                        </View>
                        <Text className="text-gray-700 text-xl font-bold mt-2">No orders found</Text>
                        {activeTab === 'pending' && currentDeliveryPartner?.isAvailable && (
                            <Text className="text-gray-500 mt-2 text-center px-4 leading-5">
                                You'll be notified when new orders are assigned to you
                            </Text>
                        )}

                        {activeTab === 'pending' && !currentDeliveryPartner?.isAvailable && (
                            <TouchableOpacity
                                className="mt-6 bg-madder px-6 py-3 rounded-xl flex-row items-center"
                                style={SHADOWS.medium}
                                onPress={handleToggleAvailability}
                            >
                                <MaterialIcons name="check-circle" size={20} color="white" />
                                <Text className="text-white font-bold ml-2">Set Available</Text>
                            </TouchableOpacity>
                        )}
                    </View>
                }
            />

            {/* Order Details Modal */}
            <Modal
                visible={showOrderDetails}
                transparent={true}
                animationType="fade"
                onRequestClose={() => {
                    // Animate the modal out
                    Animated.parallel([
                        Animated.timing(modalSlideAnim, {
                            toValue: 300,
                            duration: 250,
                            useNativeDriver: true,
                            easing: Easing.in(Easing.cubic)
                        }),
                        Animated.timing(modalFadeAnim, {
                            toValue: 0,
                            duration: 200,
                            useNativeDriver: true
                        })
                    ]).start(() => {
                        setShowOrderDetails(false);
                    });
                }}
            >
                <View className="flex-1 bg-black/50 justify-end">
                    <Animated.View
                        className="bg-white rounded-t-3xl p-6 pb-8"
                        style={[
                            SHADOWS.large,
                            {
                                transform: [{ translateY: modalSlideAnim }],
                                opacity: modalFadeAnim
                            }
                        ]}>
                        <View className="flex-row justify-between items-center mb-4">
                            <View className="flex-row items-center">
                                <View className="bg-madder/10 p-2 rounded-xl mr-2">
                                    <MaterialIcons name="receipt-long" size={24} color={COLORS.primary} />
                                </View>
                                <Text className="text-xl font-bold text-gray-800">Order Details</Text>
                            </View>
                            <TouchableOpacity
                                className="bg-gray-100 p-2 rounded-full"
                                onPress={() => {
                                    // Animate the modal out
                                    Animated.parallel([
                                        Animated.timing(modalSlideAnim, {
                                            toValue: 300,
                                            duration: 250,
                                            useNativeDriver: true,
                                            easing: Easing.in(Easing.cubic)
                                        }),
                                        Animated.timing(modalFadeAnim, {
                                            toValue: 0,
                                            duration: 200,
                                            useNativeDriver: true
                                        })
                                    ]).start(() => {
                                        setShowOrderDetails(false);
                                    });
                                }}
                            >
                                <MaterialIcons name="close" size={20} color={COLORS.darkGray} />
                            </TouchableOpacity>
                        </View>

                        {selectedOrder && (
                            <>
                                <LinearGradient
                                    colors={[COLORS.primaryLight, COLORS.primary]}
                                    start={{ x: 0, y: 0 }}
                                    end={{ x: 1, y: 1 }}
                                    className="p-4 rounded-xl mb-4"
                                >
                                    <View className="flex-row justify-between items-center mb-3">
                                        <View>
                                            <Text className="text-lg font-bold text-white">Order #{selectedOrder.orderNumber}</Text>
                                            <Text className="text-white/80">{selectedOrder.date} • {selectedOrder.time}</Text>
                                        </View>

                                        <View className="bg-white/20 px-3 py-1 rounded-full">
                                            <View className="flex-row items-center">
                                                <MaterialIcons
                                                    name={ORDER_STATUS[selectedOrder.status]?.icon || 'help'}
                                                    size={14}
                                                    color="white"
                                                />
                                                <Text className="font-medium text-sm text-white ml-1">
                                                    {ORDER_STATUS[selectedOrder.status]?.label || 'Unknown'}
                                                </Text>
                                            </View>
                                        </View>
                                    </View>

                                    {/* Expected Delivery Information - Highlighted */}
                                    {(selectedOrder.expectedDeliveryDate || selectedOrder.expectedDeliveryTime) && (
                                        <View className="mb-2 bg-white/15 p-3 rounded-md">
                                            <View className="flex-row items-center">
                                                <MaterialIcons name="schedule" size={18} color="#FFFFFF" />
                                                <View className="ml-2">
                                                    <Text className="text-xs text-white/80 font-medium">EXPECTED DELIVERY</Text>
                                                    <Text className="text-base font-bold text-white">
                                                        {selectedOrder.expectedDeliveryDate}
                                                        {selectedOrder.expectedDeliveryTime ? ` • ${selectedOrder.expectedDeliveryTime}` : ''}
                                                    </Text>
                                                </View>
                                            </View>
                                        </View>
                                    )}

                                    {/* Delivered Time Information */}
                                    {selectedOrder.status === 'delivered' && (selectedOrder.deliveredDate || selectedOrder.deliveredTime) && (
                                        <View className="mb-2 bg-green-600/30 p-3 rounded-md">
                                            <View className="flex-row items-center">
                                                <MaterialIcons name="check-circle" size={18} color="#FFFFFF" />
                                                <View className="ml-2">
                                                    <Text className="text-xs text-white/80 font-medium">DELIVERED ON</Text>
                                                    <Text className="text-base font-bold text-white">
                                                        {selectedOrder.deliveredDate}
                                                        {selectedOrder.deliveredTime ? ` • ${selectedOrder.deliveredTime}` : ''}
                                                    </Text>
                                                </View>
                                            </View>
                                        </View>
                                    )}

                                    <View className="flex-row items-center justify-between mt-2">
                                        <View className="flex-row items-center">
                                            <Text className="text-white font-bold text-lg">₹{selectedOrder.total}</Text>
                                            <Text className="ml-1 text-white/80">({selectedOrder.items} items)</Text>
                                        </View>

                                        {/* Payment Method Badge */}
                                        <View className="bg-white/20 px-3 py-1 rounded-full">
                                            <View className="flex-row items-center">
                                                <MaterialIcons
                                                    name={['COD', 'cod'].includes(selectedOrder.paymentMethod) ? 'payments' : 'account-balance'}
                                                    size={14}
                                                    color="white"
                                                />
                                                <Text className="text-white text-xs font-medium ml-1">
                                                    {['COD', 'cod'].includes(selectedOrder.paymentMethod) ? 'COD' :
                                                     ['UPI', 'upi'].includes(selectedOrder.paymentMethod) ? 'UPI' :
                                                     selectedOrder.paymentMethod || 'N/A'}
                                                </Text>
                                            </View>
                                        </View>
                                    </View>
                                </LinearGradient>

                                <View className="bg-gray-50 p-4 rounded-xl mb-4">
                                    <Text className="text-gray-800 font-bold mb-3">Customer Information</Text>

                                    {/* Customer info with call button */}
                                    <View className="flex-row items-start mb-4">
                                        <View className="w-10 h-10 bg-madder/10 rounded-full items-center justify-center">
                                            <MaterialIcons name="person" size={20} color={COLORS.primary} />
                                        </View>
                                        <View className="ml-3 flex-1">
                                            <Text className="text-xs text-madder font-bold">CUSTOMER</Text>
                                            <Text className="text-gray-800 font-medium">{selectedOrder.customer}</Text>
                                            <Text className="text-gray-500 text-sm">{selectedOrder.phone}</Text>
                                        </View>
                                        <TouchableOpacity
                                            className="bg-green-100 p-2.5 rounded-lg"
                                            onPress={() => handleCall(selectedOrder.phone, selectedOrder.customer)}
                                        >
                                            <MaterialIcons name="call" size={22} color="#16A34A" />
                                        </TouchableOpacity>
                                    </View>

                                    {/* Delivery address with directions button */}
                                    <View className="flex-row items-start">
                                        <View className="w-10 h-10 bg-madder/10 rounded-full items-center justify-center">
                                            <MaterialIcons name="location-on" size={20} color={COLORS.primary} />
                                        </View>
                                        <View className="ml-3 flex-1">
                                            <Text className="text-xs text-madder font-bold">DELIVERY ADDRESS</Text>
                                            <Text className="text-gray-700 mt-1">{selectedOrder.address}</Text>
                                        </View>
                                        <TouchableOpacity
                                            className="bg-blue-100 p-2.5 rounded-lg"
                                            onPress={() => handleMap(selectedOrder)}
                                        >
                                            <MaterialIcons name="directions" size={22} color="#2563EB" />
                                        </TouchableOpacity>
                                    </View>
                                </View>

                                {/* Payment Information Section */}
                                <View className="bg-blue-50 p-4 rounded-xl mb-4">
                                    <Text className="text-gray-800 font-bold mb-3">Payment Information</Text>

                                    <View className="flex-row items-center">
                                        <View className="w-10 h-10 bg-blue-100 rounded-full items-center justify-center">
                                            <MaterialIcons
                                                name={['COD', 'cod'].includes(selectedOrder.paymentMethod) ? 'payments' : 'account-balance'}
                                                size={20}
                                                color="#2563EB"
                                            />
                                        </View>
                                        <View className="ml-3 flex-1">
                                            <Text className="text-xs text-blue-600 font-bold">PAYMENT MODE</Text>
                                            <Text className="text-gray-800 font-medium text-lg">
                                                {['COD', 'cod'].includes(selectedOrder.paymentMethod) ? 'Cash on Delivery' :
                                                 ['UPI', 'upi'].includes(selectedOrder.paymentMethod) ? 'UPI Payment' :
                                                 selectedOrder.paymentMethod || 'Not specified'}
                                            </Text>
                                            {['COD', 'cod'].includes(selectedOrder.paymentMethod) && (
                                                <Text className="text-gray-600 text-sm mt-1">
                                                    Collect ₹{selectedOrder.total} from customer
                                                </Text>
                                            )}
                                            {['UPI', 'upi'].includes(selectedOrder.paymentMethod) && (
                                                <Text className="text-green-600 text-sm mt-1">
                                                    Payment already completed
                                                </Text>
                                            )}
                                        </View>
                                    </View>
                                </View>

                                {selectedOrder.status === 'pending' && (
                                    <TouchableOpacity
                                        className="bg-madder py-3 rounded-xl items-center mt-2 flex-row justify-center"
                                        onPress={() => {
                                            updateOrderStatus(selectedOrder.id, 'in-transit');
                                            setShowOrderDetails(false);
                                        }}
                                    >
                                        <MaterialIcons name="local-shipping" size={20} color="white" />
                                        <Text className="text-white font-bold ml-2">Start Delivery</Text>
                                    </TouchableOpacity>
                                )}

                                {selectedOrder.status === 'in-transit' && (
                                    <>
                                        <View className="mb-4">
                                            <Text className="text-gray-700 font-medium mb-2">Delivery Notes (optional)</Text>
                                            <TextInput
                                                className="border border-gray-300 rounded-xl p-3 text-gray-700"
                                                placeholder="Add notes about the delivery..."
                                                multiline
                                                numberOfLines={3}
                                                value={deliveryNote}
                                                onChangeText={setDeliveryNote}
                                            />
                                        </View>

                                        <TouchableOpacity
                                            className="bg-green-600 py-3 rounded-xl items-center flex-row justify-center"
                                            onPress={handleCompleteDelivery}
                                        >
                                            <MaterialIcons name="check-circle" size={20} color="white" />
                                            <Text className="text-white font-bold ml-2">Complete Delivery</Text>
                                        </TouchableOpacity>
                                    </>
                                )}

                                {selectedOrder.status === 'delivered' && selectedOrder.deliveryNote && (
                                    <View className="bg-gray-50 p-4 rounded-xl mb-4">
                                        <View className="flex-row items-center mb-2">
                                            <MaterialIcons name="notes" size={18} color={COLORS.darkGray} />
                                            <Text className="text-gray-700 font-medium ml-2">Delivery Notes</Text>
                                        </View>
                                        <Text className="text-gray-600 bg-white p-3 rounded-lg border border-gray-100">
                                            {selectedOrder.deliveryNote}
                                        </Text>
                                    </View>
                                )}
                            </>
                        )}
                    </Animated.View>
                </View>
            </Modal>

            {/* Success Modal */}
            <Modal
                visible={showSuccessModal}
                transparent={true}
                animationType="fade"
            >
                <View className="flex-1 justify-center items-center bg-black/40">
                    <View className="bg-white rounded-2xl p-6 items-center m-4 shadow-lg" style={SHADOWS.large}>
                        <View className="w-20 h-20 bg-green-100 rounded-full items-center justify-center mb-4">
                            <MaterialIcons name="check-circle" size={48} color="#16A34A" />
                        </View>
                        <Text className="text-xl font-bold text-gray-800 mb-2 text-center">{successMessage}</Text>
                        <Text className="text-gray-500 text-center mb-4">Your dashboard will update shortly</Text>
                        <TouchableOpacity
                            className="bg-madder/10 px-6 py-2 rounded-xl"
                            onPress={() => setShowSuccessModal(false)}
                        >
                            <Text className="text-madder font-bold">Dismiss</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>

            {/* Custom Alert Modal */}
            <CustomAlertModal
                visible={alertVisible}
                title={alertTitle}
                message={alertMessage}
                icon={alertIcon}
                buttons={alertButtons}
                onClose={() => setAlertVisible(false)}
            />
        </View>
    );
};

export default DeliveryPartnerDashboard;
