import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { View, Text, FlatList, TouchableOpacity, TextInput, RefreshControl, Alert, Linking, Platform, Animated, Easing } from 'react-native';
import { MaterialIcons } from "@expo/vector-icons";
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import { getDeliveryPartnerOrders, updateOrderStatus } from '../../utils/api/deliveryApi';
import { useDeliveryPartner } from '../../context/DeliveryPartnerContext';
import { refreshAccessToken } from '../../utils/tokenRefresh';
import { getUserData } from '../../utils/authStorage';
import { COLORS, SHADOWS, CARD_STYLES, BUTTON_STYLES, TEXT_STYLES, ORDER_STATUS } from '../../styles/deliveryTheme';
import { openMapsWithDirections } from '../../utils/locationUtils';
import CustomAlertModal from '../../Components/delivery/CustomAlertModal';

const DeliveryPartnerOrdersScreen = () => {
    const navigation = useNavigation();
    const { currentDeliveryPartner, fetchDeliveryPartnerOrders, toggleAvailability } = useDeliveryPartner();
    const [selectedStatus, setSelectedStatus] = useState('pending');
    const [orders, setOrders] = useState([]);
    const [refreshing, setRefreshing] = useState(false);

    // Custom alert state
    const [alertVisible, setAlertVisible] = useState(false);
    const [alertTitle, setAlertTitle] = useState('');
    const [alertMessage, setAlertMessage] = useState('');
    const [alertIcon, setAlertIcon] = useState('info');
    const [alertButtons, setAlertButtons] = useState([]);

    // Animation values
    const modalSlideAnim = useRef(new Animated.Value(300)).current;
    const modalFadeAnim = useRef(new Animated.Value(0)).current;

    // Success modal state
    const [showSuccessModal, setShowSuccessModal] = useState(false);
    const [successMessage, setSuccessMessage] = useState('');

    // Fetch orders on component mount and when currentDeliveryPartner changes
    useEffect(() => {
        let isMounted = true;
        let loadingTimeout;

        const fetchOrdersWithTimeout = async () => {
            if (!isMounted) return;

            try {
                // Set loading timeout to prevent infinite loading
                loadingTimeout = setTimeout(() => {
                    if (isMounted && refreshing) {
                        console.log('OrdersScreen: Loading timeout reached, stopping refresh');
                        setRefreshing(false);
                    }
                }, 10000); // Reduced from 15s to 10s

                if (currentDeliveryPartner) {
                    await fetchOrders();
                } else {
                    setRefreshing(true);
                    const ordersFromContext = await fetchDeliveryPartnerOrders();
                    if (isMounted) {
                        if (ordersFromContext && ordersFromContext.length > 0) {
                            processOrders(ordersFromContext);
                        } else {
                            setOrders([]);
                        }
                        setRefreshing(false);
                    }
                }
            } catch (error) {
                if (isMounted) {
                    console.error('OrdersScreen: Error fetching orders:', error);
                    setOrders([]);
                    setRefreshing(false);
                }
            } finally {
                if (loadingTimeout) {
                    clearTimeout(loadingTimeout);
                }
            }
        };

        fetchOrdersWithTimeout();

        // Cleanup function
        return () => {
            isMounted = false;
            if (loadingTimeout) {
                clearTimeout(loadingTimeout);
            }
        };
    }, [currentDeliveryPartner]);

    // Add a focus listener to refresh data when tab is focused
    useEffect(() => {
        const unsubscribe = navigation.addListener('focus', () => {
            if (currentDeliveryPartner && !refreshing) {
                fetchOrders();
            }
        });

        return unsubscribe;
    }, [navigation, currentDeliveryPartner, refreshing]);

    // Fetch orders from API
    // Helper function to show custom alert
    const showCustomAlert = (title, message, icon = 'info', buttons = [{ text: "OK", style: "default" }]) => {
        setAlertTitle(title);
        setAlertMessage(message);
        setAlertIcon(icon);
        setAlertButtons(buttons);
        setAlertVisible(true);
    };

    const fetchOrders = async (retryCount = 0) => {
        try {
            // Get user data for debugging
            let userData = null;
            try {
                userData = await getUserData();
                console.log('OrdersScreen: User data:', JSON.stringify(userData, null, 2));
            } catch (userDataError) {
                console.error('OrdersScreen: Error getting user data:', userDataError);
            }

            if (!currentDeliveryPartner) {
                console.log('OrdersScreen: No delivery partner available, trying to use user data');

                if (userData && (userData.userType === 'DELIVERY_PARTNER' || userData.userType === 'delivery_partner')) {
                    console.log('OrdersScreen: Using user data as delivery partner');
                    // This will be used for the next API call
                } else {
                    console.log('OrdersScreen: No delivery partner data available');
                    setRefreshing(false);
                    return;
                }
            }

            setRefreshing(true);
            console.log('OrdersScreen: Fetching orders for delivery partner...',
                currentDeliveryPartner?._id || currentDeliveryPartner?.id || userData?._id || userData?.id);

            let response;
            try {
                console.log('OrdersScreen: Calling getDeliveryPartnerOrders API function');
                response = await getDeliveryPartnerOrders();
                console.log('OrdersScreen: API response received:', JSON.stringify(response, null, 2));
            } catch (error) {
                // If 401 error, try to refresh token and retry
                if (error.response && error.response.status === 401) {
                    console.log('OrdersScreen: Token expired, refreshing...');
                    try {
                        await refreshAccessToken();
                        // Retry with new token
                        console.log('OrdersScreen: Token refreshed, retrying API call');
                        response = await getDeliveryPartnerOrders();
                    } catch (refreshError) {
                        console.error('OrdersScreen: Token refresh failed:', refreshError);

                        // Show authentication error to user
                        Alert.alert(
                            "Authentication Error",
                            "Your session has expired. Please log in again.",
                            [{ text: "OK", onPress: () => navigation.navigate('PreLoginScreen') }]
                        );

                        setRefreshing(false);
                        return;
                    }
                } else {
                    // For other errors, try the context function as fallback
                    console.error('OrdersScreen: Error fetching orders directly:', error);
                    console.log('OrdersScreen: Trying context function as fallback');

                    try {
                        response = await fetchDeliveryPartnerOrders();
                        console.log('OrdersScreen: Context function returned:',
                            Array.isArray(response) ? `${response.length} orders` : typeof response);

                        if (!response || (Array.isArray(response) && response.length === 0)) {
                            // Show error if both methods fail
                            Alert.alert(
                                "Error",
                                "Failed to fetch order history. Please try again later.",
                                [{ text: "OK" }]
                            );
                            setRefreshing(false);
                            return;
                        }
                    } catch (contextError) {
                        console.error('OrdersScreen: Context function also failed:', contextError);
                        Alert.alert(
                            "Error",
                            "Failed to fetch order history. Please try again later.",
                            [{ text: "OK" }]
                        );
                        setRefreshing(false);
                        return;
                    }
                }
            }

            console.log(`OrdersScreen: Received ${response?.length || 0} orders from API`);

            // Handle empty response
            if (!response) {
                console.log('OrdersScreen: No orders received from server, setting empty array');
                setOrders([]);
                setRefreshing(false);
                return;
            }

            let ordersArray = response;

            // Handle different response formats
            if (!Array.isArray(response)) {
                console.log('OrdersScreen: Response is not an array, trying to extract orders array');
                console.log('OrdersScreen: Response type:', typeof response);
                console.log('OrdersScreen: Response keys:', Object.keys(response));

                // Try to extract orders array if response is an object
                if (response?.orders && Array.isArray(response.orders)) {
                    console.log('OrdersScreen: Found orders array in response.orders with', response.orders.length, 'orders');
                    ordersArray = response.orders;
                } else if (response?.data?.orders && Array.isArray(response.data.orders)) {
                    console.log('OrdersScreen: Found orders array in response.data.orders with', response.data.orders.length, 'orders');
                    ordersArray = response.data.orders;
                } else {
                    console.log('OrdersScreen: Could not find orders array in response, setting empty array');

                    // Try one more approach - if it's a single order object
                    if (typeof response === 'object' && response._id) {
                        console.log('OrdersScreen: Response appears to be a single order object');
                        ordersArray = [response];
                    } else {
                        setOrders([]);
                        setRefreshing(false);
                        return;
                    }
                }
            }

            console.log(`OrdersScreen: Total orders available: ${ordersArray.length}`);

            // Get the delivery partner ID
            const partnerId = currentDeliveryPartner?._id || currentDeliveryPartner?.id ||
                              userData?._id || userData?.id;

            console.log(`OrdersScreen: Filtering orders for delivery partner ${partnerId}`);

            // Filter orders for the current delivery partner
            const filteredOrders = ordersArray.filter(order => {
                try {
                    // Check if the order has a deliveryPartner field
                    if (!order.deliveryPartner) {
                        // Also check for other fields that might contain the delivery partner ID
                        const alternativeFields = [
                            'deliveryPartnerId', 'deliveryPartnerID',
                            'delivery_partner_id', 'assignedTo', 'assignedDeliveryPartner'
                        ];

                        for (const field of alternativeFields) {
                            if (order[field]) {
                                const fieldValue = order[field];
                                const fieldValueStr = String(typeof fieldValue === 'object' ?
                                    (fieldValue._id || fieldValue.id) : fieldValue);
                                const currentIdStr = String(partnerId);

                                if (fieldValueStr === currentIdStr) {
                                    return true;
                                }
                            }
                        }

                        // Check for timestamps in the order that match the delivery partner
                        if (order.deliveryPartnerAssignedAt &&
                            order.deliveryStartedAt &&
                            order.deliveredAt) {
                            console.log('Order has delivery timestamps, might belong to this partner');
                            return true;
                        }

                        return false;
                    }

                    // Handle different formats of deliveryPartner field
                    const orderDeliveryPartnerId =
                        typeof order.deliveryPartner === 'object'
                            ? (order.deliveryPartner._id || order.deliveryPartner.id)
                            : order.deliveryPartner;

                    // Convert both to strings for comparison
                    const orderIdStr = String(orderDeliveryPartnerId);
                    const currentIdStr = String(partnerId);

                    // Direct string comparison
                    if (orderIdStr === currentIdStr) {
                        return true;
                    }

                    // Special handling for MongoDB ObjectId format
                    // Check if both are MongoDB ObjectIds (24 hex characters)
                    const isOrderIdObjectId = /^[0-9a-f]{24}$/i.test(orderIdStr);
                    const isCurrentIdObjectId = /^[0-9a-f]{24}$/i.test(currentIdStr);

                    if (isOrderIdObjectId && isCurrentIdObjectId) {
                        // For MongoDB ObjectIds, we need to compare them as strings
                        return orderIdStr === currentIdStr;
                    }

                    // Special handling for custom ID format (e.g., DP002)
                    if (typeof currentIdStr === 'string' && currentIdStr.startsWith('DP')) {
                        // Check if the order's deliveryPartner is an object with an id property
                        if (typeof order.deliveryPartner === 'object' &&
                            order.deliveryPartner.id &&
                            String(order.deliveryPartner.id) === currentIdStr) {
                            return true;
                        }

                        // Check other possible fields
                        const fieldsToCheck = [
                            'deliveryPartnerId', 'deliveryPartnerID',
                            'delivery_partner_id', 'assignedTo', 'assignedDeliveryPartner'
                        ];

                        for (const field of fieldsToCheck) {
                            if (order[field] && String(order[field]) === currentIdStr) {
                                return true;
                            }
                        }
                    }

                    // Check for timestamps in the order that match the delivery partner
                    // This is a fallback for when the IDs don't match but we have timestamps
                    if (order.deliveryPartnerAssignedAt &&
                        order.deliveryStartedAt &&
                        order.deliveredAt) {
                        // If the order has all the delivery timestamps, it's likely assigned to this partner
                        console.log('Order has delivery timestamps, checking if it belongs to this partner');
                        return true;
                    }

                    return false;
                } catch (error) {
                    console.error('Error filtering order:', error);
                    return false;
                }
            });

            console.log(`OrdersScreen: Found ${filteredOrders.length} orders for delivery partner ${partnerId}`);

            // Always check for unassigned orders that can be picked up
            console.log('OrdersScreen: Checking for unassigned orders');

            // Filter for unassigned orders (no deliveryPartner) with status PLACED or CONFIRMED
            const unassignedOrders = ordersArray.filter(order =>
                !order.deliveryPartner &&
                (order.status === 'PLACED' || order.status === 'CONFIRMED')
            );

            console.log(`OrdersScreen: Found ${unassignedOrders.length} unassigned orders that can be picked up`);

            // Always process both assigned and unassigned orders
            processOrders([...filteredOrders, ...unassignedOrders]);
        } catch (error) {
            console.error('OrdersScreen: Error fetching orders:', error);

            // Retry logic (up to 1 retry to prevent infinite loops)
            if (retryCount < 1) {
                console.log(`OrdersScreen: Retrying fetch orders (attempt ${retryCount + 1})...`);
                setTimeout(() => fetchOrders(retryCount + 1), 2000);
                return;
            }

            // Show error alert only after retries fail
            showCustomAlert(
                "Error",
                "Failed to fetch order history. Please check your connection and try again.",
                "error",
                [{ text: "Retry", onPress: () => fetchOrders(0) }]
            );
        } finally {
            setRefreshing(false);
        }
    };

    // Process orders from API response
    const processOrders = (ordersArray) => {
        try {
            if (!ordersArray || !Array.isArray(ordersArray)) {
                console.log('OrdersScreen: Invalid orders array:', ordersArray);
                setOrders([]);
                return;
            }

            console.log(`OrdersScreen: Processing ${ordersArray.length} orders`);

            // Format orders from API response
            const formattedOrders = ordersArray.map(order => {
                try {
                    // Extract date and time from createdAt or orderPlacedAt
                    const orderDate = new Date(order.createdAt || order.orderPlacedAt || Date.now());
                    const formattedDate = orderDate.toLocaleDateString('en-IN');
                    const formattedTime = orderDate.toLocaleTimeString('en-IN', {
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: true
                    });

                    // Extract expected delivery date and time
                    let expectedDeliveryDate = '';
                    let expectedDeliveryTime = '';

                    // Extract delivered date and time if available
                    let deliveredDate = '';
                    let deliveredTime = '';
                    if (order.deliveredAt) {
                        try {
                            const deliveredDateTime = new Date(order.deliveredAt);
                            if (!isNaN(deliveredDateTime.getTime())) {
                                deliveredDate = deliveredDateTime.toLocaleDateString("en-IN", {
                                    day: "numeric", month: "short", year: "numeric"
                                });
                                deliveredTime = deliveredDateTime.toLocaleTimeString("en-IN", {
                                    hour: '2-digit', minute: '2-digit', hour12: true
                                });
                            }
                        } catch (error) {
                            console.error('Error parsing delivered date:', error);
                        }
                    }

                    // First check if we have the display info field
                    if (order.expectedDeliveryInfo) {
                        if (typeof order.expectedDeliveryInfo === 'string') {
                            expectedDeliveryDate = order.expectedDeliveryInfo;
                        } else if (typeof order.expectedDeliveryInfo === 'object') {
                            expectedDeliveryDate = order.expectedDeliveryInfo.formattedDate ||
                                (order.expectedDeliveryInfo.day === "Today"
                                    ? new Date().toLocaleDateString("en-IN", { day: "numeric", month: "short", year: "numeric" })
                                    : new Date(Date.now() + 86400000).toLocaleDateString("en-IN", { day: "numeric", month: "short", year: "numeric" }));
                            expectedDeliveryTime = order.expectedDeliveryInfo.time || '';
                        }
                    }
                    // If no display info, try to parse from the actual date field
                    else if (order.expectedDelivery) {
                        try {
                            const deliveryDate = new Date(order.expectedDelivery);
                            if (!isNaN(deliveryDate.getTime())) {
                                expectedDeliveryDate = deliveryDate.toLocaleDateString("en-IN", {
                                    day: "numeric", month: "short", year: "numeric"
                                });
                                expectedDeliveryTime = deliveryDate.toLocaleTimeString("en-IN", {
                                    hour: '2-digit', minute: '2-digit', hour12: true
                                });
                            }
                        } catch (error) {
                            console.error('Error parsing expected delivery date:', error);

                            // Fallback for legacy format
                            if (typeof order.expectedDelivery === 'string') {
                                expectedDeliveryDate = order.expectedDelivery;
                            } else if (typeof order.expectedDelivery === 'object') {
                                expectedDeliveryDate = order.expectedDelivery.formattedDate ||
                                    (order.expectedDelivery.day === "Today"
                                        ? new Date().toLocaleDateString("en-IN", { day: "numeric", month: "short", year: "numeric" })
                                        : new Date(Date.now() + 86400000).toLocaleDateString("en-IN", { day: "numeric", month: "short", year: "numeric" }));
                                expectedDeliveryTime = order.expectedDelivery.time || '';
                            }
                        }
                    }

                    // Map API status to UI status
                    let uiStatus = 'pending';
                    if (order.status === 'PLACED') uiStatus = 'pending';
                    if (order.status === 'CONFIRMED') uiStatus = 'pending';
                    if (order.status === 'PREPARING') uiStatus = 'pending';
                    if (order.status === 'OUT_FOR_DELIVERY') uiStatus = 'in-transit';
                    if (order.status === 'DELIVERED') uiStatus = 'delivered';
                    if (order.status === 'CANCELLED') uiStatus = 'cancelled';

                    // Also check deliveryStatus if it exists (from DeliveryPartner.orders array)
                    if (order.deliveryStatus) {
                        if (order.deliveryStatus === 'PLACED') uiStatus = 'pending';
                        if (order.deliveryStatus === 'CONFIRMED') uiStatus = 'pending';
                        if (order.deliveryStatus === 'PREPARING') uiStatus = 'pending';
                        if (order.deliveryStatus === 'OUT_FOR_DELIVERY') uiStatus = 'in-transit';
                        if (order.deliveryStatus === 'DELIVERED') uiStatus = 'delivered';
                        if (order.deliveryStatus === 'CANCELLED') uiStatus = 'cancelled';
                    }

                    // Calculate number of items
                    const itemCount = order.items ? order.items.length : 0;

                    // Get customer info from userId
                    const customerName = order.userId?.name || 'Customer';
                    const customerPhone = order.userId?.number || order.userId?.phone || '';

                    // Format address - handle both string and object formats
                    let address = 'No address provided';
                    let coordinates = null;

                    // Extract coordinates from deliveryCoordinates if available
                    if (order.deliveryCoordinates &&
                        order.deliveryCoordinates.latitude &&
                        order.deliveryCoordinates.longitude) {
                        coordinates = {
                            latitude: order.deliveryCoordinates.latitude,
                            longitude: order.deliveryCoordinates.longitude
                        };
                    }

                    if (typeof order.deliveryAddress === 'string') {
                        address = order.deliveryAddress;
                    } else if (typeof order.deliveryAddress === 'object' && order.deliveryAddress !== null) {
                        // Extract coordinates from deliveryAddress if available
                        if (!coordinates) {
                            // Check for nested coordinates structure first
                            if (order.deliveryAddress.coordinates &&
                                order.deliveryAddress.coordinates.latitude &&
                                order.deliveryAddress.coordinates.longitude) {
                                coordinates = {
                                    latitude: order.deliveryAddress.coordinates.latitude,
                                    longitude: order.deliveryAddress.coordinates.longitude
                                };
                            }
                            // Then check for flat coordinates
                            else if (order.deliveryAddress.latitude && order.deliveryAddress.longitude) {
                                coordinates = {
                                    latitude: order.deliveryAddress.latitude,
                                    longitude: order.deliveryAddress.longitude
                                };
                            }
                        }

                        if (order.deliveryAddress.fullAddress) {
                            address = order.deliveryAddress.fullAddress;
                        } else {
                            // Construct from parts
                            const parts = [];
                            if (order.deliveryAddress.doorNo) parts.push(order.deliveryAddress.doorNo);
                            if (order.deliveryAddress.streetName) parts.push(order.deliveryAddress.streetName);
                            if (order.deliveryAddress.area) parts.push(order.deliveryAddress.area);
                            if (order.deliveryAddress.district) parts.push(order.deliveryAddress.district);
                            if (order.deliveryAddress.pinCode || order.deliveryAddress.pincode)
                                parts.push(order.deliveryAddress.pinCode || order.deliveryAddress.pincode);

                            address = parts.join(', ');
                        }
                    }

                    // No earnings calculation needed

                    // Function to check if order is assigned to current delivery partner
                    const isOrderAssignedToCurrentPartner = (order) => {
                        if (!order.deliveryPartner || !currentDeliveryPartner) {
                            return false;
                        }

                        // Convert both IDs to strings for comparison
                        const orderPartnerId = typeof order.deliveryPartner === 'object'
                            ? order.deliveryPartner._id?.toString()
                            : order.deliveryPartner.toString();

                        const currentPartnerId = currentDeliveryPartner._id?.toString() || currentDeliveryPartner.id?.toString();

                        return orderPartnerId === currentPartnerId;
                    };

                    // Check if this is an assigned or unassigned order
                    const isAssigned = order.isAssigned === undefined
                        ? isOrderAssignedToCurrentPartner(order)
                        : order.isAssigned;

                    // Determine if this order can be picked up
                    // An order can be picked up if:
                    // 1. It's not already assigned to any delivery partner
                    // 2. It has a status of PLACED or CONFIRMED
                    // 3. The current delivery partner is available
                    const canPickup = !isAssigned &&
                        ['PLACED', 'CONFIRMED'].includes(order.status) &&
                        currentDeliveryPartner?.isAvailable;

                    return {
                        id: order._id,
                        orderNumber: order.orderNumber || 'N/A',
                        customer: customerName,
                        phone: customerPhone,
                        address: address,
                        status: uiStatus,
                        items: itemCount,
                        total: order.totalAmount || 0,
                        date: formattedDate,
                        time: formattedTime,
                        distance: order.distance || '~',
                        isAssigned: isAssigned,
                        canPickup: canPickup,
                        expectedDeliveryDate: expectedDeliveryDate,
                        expectedDeliveryTime: expectedDeliveryTime,
                        deliveredDate: deliveredDate,
                        deliveredTime: deliveredTime,
                        coordinates: coordinates, // Add coordinates for map view
                        paymentMethod: order.paymentMethod || 'Not specified',
                        // Keep original data for reference
                        originalOrder: order
                    };
                } catch (itemError) {
                    console.error('OrdersScreen: Error processing order item:', itemError, order);
                    // Return a default formatted order if there's an error
                    return {
                        id: order._id || 'unknown',
                        orderNumber: order.orderNumber || 'N/A',
                        customer: 'Error loading customer',
                        phone: '',
                        address: 'Error loading address',
                        status: 'pending',
                        items: 0,
                        total: 0,
                        date: new Date().toLocaleDateString('en-IN'),
                        time: new Date().toLocaleTimeString('en-IN'),
                        distance: '~',
                        isAssigned: false,
                        canPickup: false,
                        expectedDeliveryDate: '',
                        expectedDeliveryTime: '',
                        deliveredDate: '',
                        deliveredTime: '',
                        coordinates: null, // No coordinates for error case
                        originalOrder: order
                    };
                }
            });

            console.log(`OrdersScreen: Formatted ${formattedOrders.length} orders`);
            setOrders(formattedOrders);
        } catch (error) {
            console.error('OrdersScreen: Error processing orders:', error);
            setOrders([]);
        }
    };

    // Filter orders based on selected status - memoized for performance
    const filteredOrders = useMemo(() => {
        return orders.filter(order => {
            // For "all" tab, show all orders (assigned orders and unassigned orders that can be picked up)
            if (selectedStatus === 'all') {
                return order.isAssigned || order.canPickup;
            }

            // For "pending" tab, show assigned pending orders and unassigned orders that can be picked up
            if (selectedStatus === 'pending') {
                return order.status === 'pending' || order.canPickup;
            }

            // For other tabs, filter by status
            return order.status === selectedStatus;
        });
    }, [orders, selectedStatus]);

    // Function to handle order pickup
    const handleOrderPickup = async (orderId) => {
        try {
            // Check if delivery partner is available
            if (!currentDeliveryPartner?.isAvailable) {
                showCustomAlert(
                    "Not Available",
                    "You need to set your status to Available before picking up orders.",
                    "info",
                    [{
                        text: "Cancel",
                        style: "cancel"
                    },
                    {
                        text: "Set Available",
                        style: "default",
                        onPress: async () => {
                            try {
                                // Use the toggleAvailability function from the context
                                const partnerId = currentDeliveryPartner._id || currentDeliveryPartner.id;
                                if (partnerId) {
                                    await toggleAvailability(partnerId);

                                    // Show success message
                                    setSuccessMessage('You are now available to pick up orders!');
                                    setShowSuccessModal(true);

                                    // Refresh orders first to get the latest data
                                    await fetchOrders();

                                    // After a short delay, try to pick up the order
                                    setTimeout(() => {
                                        setShowSuccessModal(false);
                                        handleOrderPickup(orderId);
                                    }, 2000);
                                } else {
                                    throw new Error("Could not determine delivery partner ID");
                                }
                            } catch (error) {
                                console.error('Error updating availability:', error);
                                showCustomAlert(
                                    "Error",
                                    "Failed to update availability status.",
                                    "error",
                                    [{ text: "OK", style: "default" }]
                                );
                            }
                        }
                    }]
                );
                return false;
            }

            // Find the order in the orders list to check if it's still available
            const orderToPickup = orders.find(o => o.id === orderId);
            if (!orderToPickup || !orderToPickup.canPickup) {
                showCustomAlert(
                    "Order Unavailable",
                    "This order is no longer available for pickup. It may have been assigned to another delivery partner.",
                    "info",
                    [{ text: "OK", style: "default", onPress: () => fetchOrders() }]
                );
                return false;
            }

            setRefreshing(true);
            console.log(`Requesting to pick up order: ${orderId}`);

            try {
                // Call the API to assign the order to this delivery partner
                console.log(`Calling updateOrderStatus with orderId: ${orderId}, assignToMe: true`);
                const response = await updateOrderStatus(orderId, null, '', true);
                console.log('Order pickup response:', JSON.stringify(response, null, 2));

                // Refresh the orders list
                await fetchOrders();

                // Show success message
                setSuccessMessage('Order has been assigned to you!');
                setShowSuccessModal(true);
                setTimeout(() => {
                    setShowSuccessModal(false);

                    // Find the order in the orders list
                    const order = orders.find(o => o.id === orderId);
                    if (order) {
                        // Automatically update the order status to OUT_FOR_DELIVERY
                        handleStatusUpdate(orderId, 'in-transit', { ...order, isAssigned: true });
                    }
                }, 2000);

                // Automatically switch to the "in-transit" tab after picking up an order
                setSelectedStatus('in-transit');

                return true;
            } catch (error) {
                // Check if the error is because the order is already assigned
                if (error.response && error.response.status === 400) {
                    // Check if the error message indicates the order is already assigned
                    const errorMessage = error.response.data?.message || '';
                    if (errorMessage.includes('already assigned')) {
                        console.log('Order is already assigned, refreshing orders list');

                        // Refresh the orders list to get the latest status
                        fetchOrders();

                        // Show a more helpful message
                        setSuccessMessage('This order has already been assigned. The orders list has been refreshed.');
                        setShowSuccessModal(true);
                        setTimeout(() => setShowSuccessModal(false), 2000);
                        return false;
                    }
                }

                // Special handling for 500 errors - these might actually be successful
                // but the server is returning an error
                if (error.response && error.response.status === 500) {
                    console.log('Server returned 500 error, but the order might have been assigned. Refreshing orders list...');

                    // Refresh the orders list to check if the order was actually assigned
                    await fetchOrders();

                    // Check if the order is now assigned to this delivery partner
                    const updatedOrder = orders.find(o => o.id === orderId);
                    if (updatedOrder && updatedOrder.isAssigned) {
                        console.log('Order appears to be assigned despite 500 error. Showing success message.');

                        // Show success message
                        setSuccessMessage('Order has been assigned to you!');
                        setShowSuccessModal(true);
                        setTimeout(() => {
                            setShowSuccessModal(false);

                            // Automatically update the order status to OUT_FOR_DELIVERY
                            handleStatusUpdate(orderId, 'in-transit', { ...updatedOrder, isAssigned: true });
                        }, 2000);

                        // Automatically switch to the "in-transit" tab
                        setSelectedStatus('in-transit');

                        return true;
                    }
                }

                // Re-throw for other errors to be caught by the outer catch block
                throw error;
            }
        } catch (error) {
            console.error('Error picking up order:', error);

            // Show error message with more details
            let errorMessage = "Failed to pick up order. Please try again.";

            if (error.response) {
                if (error.response.status === 403) {
                    errorMessage = "You are not authorized to pick up this order. It may be assigned to another delivery partner.";
                } else if (error.response.status === 500) {
                    // For 500 errors, give a more helpful message
                    errorMessage = "The server encountered an error. Please refresh the orders list to check if your order was assigned.";
                } else if (error.response.data && error.response.data.message) {
                    errorMessage = error.response.data.message;
                }
            }

            showCustomAlert(
                "Error",
                errorMessage,
                "error",
                [{
                    text: "Refresh Orders",
                    style: "default",
                    onPress: () => fetchOrders()
                }]
            );

            return false;
        } finally {
            setRefreshing(false);
        }
    };

    // Function to convert UI status to API status
    const convertUIStatusToAPIStatus = (uiStatus) => {
        console.log(`Converting UI status ${uiStatus} to API status`);
        switch (uiStatus) {
            case 'pending':
                return 'CONFIRMED';
            case 'in-transit':
                return 'OUT_FOR_DELIVERY';
            case 'delivered':
                return 'DELIVERED';
            case 'cancelled':
                return 'CANCELLED';
            default:
                return uiStatus.toUpperCase();
        }
    };

    // Function to check if an order needs to be assigned first
    const checkAndAssignOrder = async (orderId, item) => {
        try {
            // Check if the order is already assigned to this delivery partner
            if (item.isAssigned) {
                console.log(`Order ${orderId} is already assigned to this delivery partner`);
                return true;
            }

            // Check if the order is still available for pickup
            if (!item.canPickup) {
                console.log(`Order ${orderId} is not available for pickup`);
                showCustomAlert(
                    "Order Unavailable",
                    "This order is not available for pickup. It may have been assigned to another delivery partner.",
                    "info",
                    [{ text: "OK", style: "default", onPress: () => fetchOrders() }]
                );
                return false;
            }

            // If not assigned, try to assign it first
            console.log(`Order ${orderId} is not assigned to this delivery partner, attempting to assign it first`);

            // Show confirmation dialog
            return new Promise((resolve) => {
                showCustomAlert(
                    "Assign Order",
                    "This order is not assigned to you. Would you like to pick up this order?",
                    "assignment",
                    [
                        {
                            text: "Cancel",
                            style: "cancel",
                            onPress: () => resolve(false)
                        },
                        {
                            text: "Yes, Pick Up",
                            style: "default",
                            onPress: async () => {
                                try {
                                    // Call the API to assign the order
                                    const success = await handleOrderPickup(orderId);
                                    resolve(success);
                                } catch (error) {
                                    console.error('Error assigning order:', error);
                                    resolve(false);
                                }
                            }
                        }
                    ]
                );
            });
        } catch (error) {
            console.error('Error checking order assignment:', error);
            return false;
        }
    };

    // Function to handle order status update
    const handleStatusUpdate = async (orderId, newStatus, item) => {
        try {
            setRefreshing(true);

            // Check if the order is assigned to this delivery partner
            const isAssigned = await checkAndAssignOrder(orderId, item);
            if (!isAssigned) {
                console.log(`Order ${orderId} assignment check failed or was cancelled`);
                setRefreshing(false);
                return;
            }

            // Convert UI status to API status if needed
            const apiStatus = newStatus.includes('_') ? newStatus : convertUIStatusToAPIStatus(newStatus);
            console.log(`Updating order ${orderId} status to ${apiStatus}`);

            // Log the current delivery partner for debugging
            console.log('Current delivery partner:', JSON.stringify({
                id: currentDeliveryPartner?._id || currentDeliveryPartner?.id,
                name: currentDeliveryPartner?.name,
                isAvailable: currentDeliveryPartner?.isAvailable
            }, null, 2));

            // Call the API to update the order status
            const response = await updateOrderStatus(orderId, apiStatus);
            console.log('Order status update response:', JSON.stringify(response, null, 2));

            // Refresh the orders list
            await fetchOrders();

            // Automatically switch to the appropriate tab based on the new status
            if (newStatus === 'in-transit') {
                setSelectedStatus('in-transit');

                // Show success message for in-transit status
                setSuccessMessage('Order pickup confirmed!');
                setShowSuccessModal(true);
                setTimeout(() => setShowSuccessModal(false), 2000);
            } else if (newStatus === 'delivered') {
                setSelectedStatus('delivered');

                // Show a more detailed success message for delivered orders
                setSuccessMessage('Order delivered successfully!');
                setShowSuccessModal(true);
                setTimeout(() => setShowSuccessModal(false), 2000);
                return;
            } else {
                // Show success message for other status updates
                setSuccessMessage(`Order status updated to ${apiStatus}.`);
                setShowSuccessModal(true);
                setTimeout(() => setShowSuccessModal(false), 2000);
            }
        } catch (error) {
            console.error('Error updating order status:', error);

            // Special handling for 500 errors - these might actually be successful
            // but the server is returning an error
            if (error.response && error.response.status === 500) {
                console.log('Server returned 500 error, but the status might have been updated. Refreshing orders list...');

                // Refresh the orders list to check if the order status was actually updated
                await fetchOrders();

                // Check if the order status has been updated
                const updatedOrder = orders.find(o => o.id === orderId);
                if (updatedOrder && updatedOrder.status === newStatus) {
                    console.log('Order status appears to be updated despite 500 error. Showing success message.');

                    // Show success message based on the status
                    if (newStatus === 'in-transit') {
                        setSuccessMessage('Order pickup confirmed!');
                        setShowSuccessModal(true);
                        setTimeout(() => setShowSuccessModal(false), 2000);
                    } else if (newStatus === 'delivered') {
                        setSuccessMessage('Order delivered successfully!');
                        setShowSuccessModal(true);
                        setTimeout(() => setShowSuccessModal(false), 2000);
                    } else {
                        setSuccessMessage(`Order status updated to ${newStatus}.`);
                        setShowSuccessModal(true);
                        setTimeout(() => setShowSuccessModal(false), 2000);
                    }

                    // Set the appropriate tab
                    setSelectedStatus(newStatus);
                    return;
                }
            }

            // Show error message with more details
            let errorMessage = "Failed to update order status. Please try again.";

            if (error.response) {
                if (error.response.status === 403) {
                    errorMessage = "You are not authorized to update this order. It may be assigned to another delivery partner.";
                } else if (error.response.status === 500) {
                    // For 500 errors, give a more helpful message
                    errorMessage = "The server encountered an error. Please refresh the orders list to check if your order status was updated.";
                } else if (error.response.data && error.response.data.message) {
                    errorMessage = error.response.data.message;
                }
            }

            showCustomAlert(
                "Error",
                errorMessage,
                "error",
                [{
                    text: "Refresh Orders",
                    style: "default",
                    onPress: () => fetchOrders()
                }]
            );
        } finally {
            setRefreshing(false);
        }
    };

    const renderOrderItem = ({ item }) => {
        const statusConfig = ORDER_STATUS[item.status] || ORDER_STATUS.pending;

        return (
            <TouchableOpacity
                className="bg-white rounded-xl p-4 mb-4 shadow-sm"
                style={SHADOWS.small}
                activeOpacity={0.7}
            >
                <View className="flex-row justify-between items-start mb-3">
                    <View className="flex-1 mr-2">
                        <View className="flex-row items-center">
                            <View className="bg-madder/10 p-1.5 rounded-lg mr-2">
                                <MaterialIcons name="receipt" size={18} color={COLORS.primary} />
                            </View>
                            <View>
                                <Text className="text-lg font-bold text-gray-800">Order #{item.orderNumber}</Text>
                                <View className="flex-row items-center mt-0.5">
                                    <MaterialIcons name="event" size={14} color={COLORS.darkGray} />
                                    <Text className="text-gray-500 ml-1 text-xs">{item.date} • {item.time}</Text>
                                </View>
                            </View>
                        </View>

                        {/* Available badge moved below order number */}
                        {item.canPickup && (
                            <View className="mt-2 bg-green-500 px-2 py-0.5 rounded-full self-start">
                                <Text className="text-white text-xs font-bold">AVAILABLE</Text>
                            </View>
                        )}
                    </View>

                    <View className="flex-row items-center">
                        <View className={`px-3 py-1 rounded-full bg-${statusConfig.bgColor}`}>
                            <View className="flex-row items-center">
                                <MaterialIcons name={statusConfig.icon} size={14} color={statusConfig.color} />
                                <Text className={`text-xs font-medium ml-1 text-${statusConfig.textColor}`}>
                                    {statusConfig.label}
                                </Text>
                            </View>
                        </View>
                    </View>
                </View>

                {/* Expected Delivery Information - Highlighted */}
                {(item.expectedDeliveryDate || item.expectedDeliveryTime) && (
                    <View className="mb-3">
                        <LinearGradient
                            colors={[COLORS.primary, COLORS.primaryLight]}
                            start={{ x: 0, y: 0 }}
                            end={{ x: 1, y: 0 }}
                            className="p-2.5 rounded-lg"
                        >
                            <View className="flex-row items-center">
                                <View className="bg-white/20 p-1 rounded-md">
                                    <MaterialIcons name="schedule" size={16} color="#FFFFFF" />
                                </View>
                                <View className="ml-2">
                                    <Text className="text-xs text-white/80 font-medium">EXPECTED DELIVERY</Text>
                                    <Text className="text-sm font-bold text-white">
                                        {item.expectedDeliveryDate}
                                        {item.expectedDeliveryTime ? ` • ${item.expectedDeliveryTime}` : ''}
                                    </Text>
                                </View>
                            </View>
                        </LinearGradient>
                    </View>
                )}

                {/* Delivered Time Information */}
                {item.status === 'delivered' && (item.deliveredDate || item.deliveredTime) && (
                    <View className="mb-3">
                        <LinearGradient
                            colors={['#16A34A', '#22C55E']}
                            start={{ x: 0, y: 0 }}
                            end={{ x: 1, y: 0 }}
                            className="p-2.5 rounded-lg"
                        >
                            <View className="flex-row items-center">
                                <View className="bg-white/20 p-1 rounded-md">
                                    <MaterialIcons name="check-circle" size={16} color="#FFFFFF" />
                                </View>
                                <View className="ml-2">
                                    <Text className="text-xs text-white/80 font-medium">DELIVERED ON</Text>
                                    <Text className="text-sm font-bold text-white">
                                        {item.deliveredDate}
                                        {item.deliveredTime ? ` • ${item.deliveredTime}` : ''}
                                    </Text>
                                </View>
                            </View>
                        </LinearGradient>
                    </View>
                )}

                <View className="bg-gray-50 p-3 rounded-lg mb-3">
                    {/* Customer info with call button */}
                    <View className="flex-row items-start mb-3">
                        <View className="bg-madder/10 p-1 rounded-md mt-0.5">
                            <MaterialIcons name="person" size={16} color={COLORS.primary} />
                        </View>
                        <View className="ml-2 flex-1">
                            <Text className="text-xs text-madder font-bold">CUSTOMER</Text>
                            <Text className="font-bold text-gray-800">{item.customer}</Text>
                        </View>
                        <TouchableOpacity
                            className="bg-green-100 p-2 rounded-lg ml-2"
                            onPress={() => {
                                if (item.phone) {
                                    // Format phone number for display in Indian format (+91 XXXXXXXXXX)
                                    let formattedPhone = item.phone;

                                    // Remove any non-digit characters
                                    const digitsOnly = item.phone.replace(/\D/g, '');

                                    // If it's a 10-digit number, format as +91 XXXXXXXXXX
                                    if (digitsOnly.length === 10) {
                                        formattedPhone = `+91 ${digitsOnly}`;
                                    }
                                    // If it already has country code (assuming 91 for India)
                                    else if (digitsOnly.length > 10 && digitsOnly.startsWith('91')) {
                                        formattedPhone = `+${digitsOnly.substring(0, 2)} ${digitsOnly.substring(2)}`;
                                    }

                                    showCustomAlert(
                                        "Call Customer",
                                        `Would you like to call ${item.customer}?\n\n${formattedPhone}`,
                                        "call",
                                        [
                                            {
                                                text: "Cancel",
                                                style: "cancel"
                                            },
                                            {
                                                text: "Call",
                                                style: 'default',
                                                onPress: () => {
                                                    const phoneUrl = `tel:${item.phone.replace(/\s/g, '')}`;
                                                    Linking.canOpenURL(phoneUrl)
                                                        .then(supported => {
                                                            if (supported) {
                                                                return Linking.openURL(phoneUrl);
                                                            } else {
                                                                showCustomAlert(
                                                                    "Device Error",
                                                                    "Phone calls are not supported on this device",
                                                                    "error",
                                                                    [{ text: "OK", style: 'default' }]
                                                                );
                                                            }
                                                        })
                                                        .catch(err => {
                                                            console.error('Error opening phone app:', err);
                                                            showCustomAlert(
                                                                "Error",
                                                                "Could not open phone application",
                                                                "error",
                                                                [{ text: "OK", style: 'default' }]
                                                            );
                                                        });
                                                }
                                            }
                                        ]
                                    );
                                } else {
                                    showCustomAlert(
                                        "No Phone Number",
                                        "No phone number available for this customer",
                                        "phone-disabled",
                                        [{ text: "OK", style: 'default' }]
                                    );
                                }
                            }}
                        >
                            <MaterialIcons name="call" size={20} color="#16A34A" />
                        </TouchableOpacity>
                    </View>

                    {/* Delivery address with directions button */}
                    <View className="flex-row items-start mb-3">
                        <View className="bg-madder/10 p-1 rounded-md mt-0.5">
                            <MaterialIcons name="location-on" size={16} color={COLORS.primary} />
                        </View>
                        <View className="ml-2 flex-1">
                            <Text className="text-xs text-madder font-bold">DELIVERY ADDRESS</Text>
                            <Text className="text-gray-700">{item.address}</Text>
                        </View>
                        <TouchableOpacity
                            className="bg-blue-100 p-2 rounded-lg ml-2"
                            onPress={() => {
                                if (item.address) {
                                    // Format address for display (truncate if too long)
                                    const displayAddress = item.address.length > 60
                                        ? item.address.substring(0, 57) + '...'
                                        : item.address;

                                    // Extract coordinates from various possible locations
                                    let coordinates = null;

                                    // First check if the item has coordinates directly (from our formatted object)
                                    if (item.coordinates && item.coordinates.latitude && item.coordinates.longitude) {
                                        coordinates = {
                                            latitude: item.coordinates.latitude,
                                            longitude: item.coordinates.longitude
                                        };
                                        console.log('Using coordinates from formatted item:', coordinates);
                                    }
                                    // Then check the original order object for coordinates
                                    else if (item.originalOrder) {
                                        // Check for coordinates in different formats in the original order
                                        if (item.originalOrder.deliveryAddress && item.originalOrder.deliveryAddress.coordinates &&
                                            item.originalOrder.deliveryAddress.coordinates.latitude &&
                                            item.originalOrder.deliveryAddress.coordinates.longitude) {
                                            // New format with nested coordinates
                                            coordinates = {
                                                latitude: item.originalOrder.deliveryAddress.coordinates.latitude,
                                                longitude: item.originalOrder.deliveryAddress.coordinates.longitude
                                            };
                                            console.log('Using coordinates from original order deliveryAddress.coordinates:', coordinates);
                                        } else if (item.originalOrder.deliveryAddress && item.originalOrder.deliveryAddress.latitude && item.originalOrder.deliveryAddress.longitude) {
                                            // Old format with direct lat/lng
                                            coordinates = {
                                                latitude: item.originalOrder.deliveryAddress.latitude,
                                                longitude: item.originalOrder.deliveryAddress.longitude
                                            };
                                            console.log('Using coordinates from original order deliveryAddress lat/lng:', coordinates);
                                        } else if (item.originalOrder.deliveryCoordinates &&
                                                  item.originalOrder.deliveryCoordinates.latitude &&
                                                  item.originalOrder.deliveryCoordinates.longitude) {
                                            // Check for deliveryCoordinates
                                            coordinates = {
                                                latitude: item.originalOrder.deliveryCoordinates.latitude,
                                                longitude: item.originalOrder.deliveryCoordinates.longitude
                                            };
                                            console.log('Using coordinates from original order deliveryCoordinates:', coordinates);
                                        } else if (item.originalOrder.coordinates &&
                                                  item.originalOrder.coordinates.latitude &&
                                                  item.originalOrder.coordinates.longitude) {
                                            // Direct coordinates on order
                                            coordinates = {
                                                latitude: item.originalOrder.coordinates.latitude,
                                                longitude: item.originalOrder.coordinates.longitude
                                            };
                                            console.log('Using coordinates from original order coordinates:', coordinates);
                                        } else if (item.originalOrder.latitude && item.originalOrder.longitude) {
                                            // Direct lat/lng on order
                                            coordinates = {
                                                latitude: item.originalOrder.latitude,
                                                longitude: item.originalOrder.longitude
                                            };
                                            console.log('Using coordinates from original order lat/lng:', coordinates);
                                        }
                                    }

                                    // Log the item data for debugging
                                    console.log('Map navigation requested for item:', {
                                        id: item.id,
                                        orderNumber: item.orderNumber,
                                        hasCoordinates: !!coordinates,
                                        coordinates: coordinates
                                    });

                                    showCustomAlert(
                                        "Open Maps",
                                        `Would you like to get directions to this address?\n\n${displayAddress}`,
                                        "directions",
                                        [
                                            {
                                                text: "Cancel",
                                                style: "cancel"
                                            },
                                            {
                                                text: "Open Maps",
                                                style: "default",
                                                onPress: () => {
                                                    // Use the utility function for consistent behavior
                                                    // Always prioritize coordinates over address text
                                                    openMapsWithDirections(item.address, coordinates, true);
                                                }
                                            }
                                        ]
                                    );
                                } else {
                                    showCustomAlert(
                                        "No Address",
                                        "No address available for this delivery",
                                        "location-off",
                                        [{ text: "OK", style: 'default' }]
                                    );
                                }
                            }}
                        >
                            <MaterialIcons name="directions" size={20} color="#2563EB" />
                        </TouchableOpacity>
                    </View>

                    {/* Payment Method Information */}
                    <View className="flex-row items-center mb-2">
                        <View className="bg-blue-100 p-1 rounded-md">
                            <MaterialIcons
                                name={['COD', 'cod'].includes(item.paymentMethod) ? 'payments' : 'account-balance'}
                                size={16}
                                color="#2563EB"
                            />
                        </View>
                        <View className="ml-2 flex-1">
                            <Text className="text-xs text-blue-600 font-bold">PAYMENT MODE</Text>
                            <Text className="text-gray-800 font-medium">
                                {['COD', 'cod'].includes(item.paymentMethod) ? 'Cash on Delivery' :
                                 ['UPI', 'upi'].includes(item.paymentMethod) ? 'UPI Payment' :
                                 item.paymentMethod || 'Not specified'}
                            </Text>
                        </View>
                        {['COD', 'cod'].includes(item.paymentMethod) && (
                            <View className="bg-orange-100 px-2 py-1 rounded-md">
                                <Text className="text-orange-700 text-xs font-medium">Collect Cash</Text>
                            </View>
                        )}
                        {['UPI', 'upi'].includes(item.paymentMethod) && (
                            <View className="bg-green-100 px-2 py-1 rounded-md">
                                <Text className="text-green-700 text-xs font-medium">Paid</Text>
                            </View>
                        )}
                    </View>

                    <View className="flex-row justify-between pt-2 border-t border-gray-200">
                        <View className="flex-row items-center">
                            <MaterialIcons name="shopping-bag" size={16} color={COLORS.darkGray} />
                            <Text className="text-gray-600 ml-1 font-medium">{item.items} items</Text>
                        </View>

                        <View className="bg-madder/10 px-3 py-1 rounded-lg">
                            <Text className="text-madder font-bold">₹{item.total}</Text>
                        </View>
                    </View>
                </View>

                <View className="flex-row justify-end items-center">
                    {item.canPickup && currentDeliveryPartner?.isAvailable ? (
                        <TouchableOpacity
                            className="bg-madder px-5 py-2.5 rounded-xl flex-row items-center"
                            style={SHADOWS.medium}
                            onPress={() => handleOrderPickup(item.id)}
                        >
                            <MaterialIcons name="assignment" size={20} color="white" />
                            <Text className="text-white font-bold ml-2">Pick Up Order</Text>
                        </TouchableOpacity>
                    ) : !item.isAssigned && (
                        <View className="bg-gray-200 px-5 py-2.5 rounded-xl flex-row items-center">
                            <MaterialIcons
                                name={!currentDeliveryPartner?.isAvailable ? "error-outline" : "block"}
                                size={20}
                                color="#6B7280"
                            />
                            <Text className="text-gray-600 font-medium ml-2">
                                {!currentDeliveryPartner?.isAvailable ? 'Set Available to Pick Up' : 'Cannot Pick Up'}
                            </Text>
                        </View>
                    )}
                </View>

                {/* Status update buttons for assigned orders */}
                {item.isAssigned && (
                    <View className="mt-3">
                        {item.status === 'in-transit' && (
                            <TouchableOpacity
                                className="bg-green-600 py-3 rounded-xl flex-row justify-center items-center"
                                style={SHADOWS.medium}
                                onPress={() => handleStatusUpdate(item.id, 'delivered', item)}
                            >
                                <MaterialIcons name="check-circle" size={20} color="white" />
                                <Text className="text-white font-bold ml-2">Mark as Delivered</Text>
                            </TouchableOpacity>
                        )}

                        {item.status === 'pending' && (
                            <TouchableOpacity
                                className="bg-blue-500 py-3 rounded-xl flex-row justify-center items-center"
                                style={SHADOWS.medium}
                                onPress={() => handleStatusUpdate(item.id, 'in-transit', item)}
                            >
                                <MaterialIcons name="local-shipping" size={20} color="white" />
                                <Text className="text-white font-bold ml-2">Start Delivery</Text>
                            </TouchableOpacity>
                        )}
                    </View>
                )}
            </TouchableOpacity>
        );
    };

    return (
        <View className="flex-1 bg-snow">
            <LinearGradient
                colors={[COLORS.primaryDark, COLORS.primary]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                className="p-4 pt-14 pb-4 rounded-b-3xl"
            >
                <View className="flex-row justify-between items-center">
                    <View className="flex-row items-center">
                        <MaterialIcons name="history" size={24} color="white" />
                        <Text className="text-xl text-white font-bold ml-2">Order History</Text>
                    </View>

                    {currentDeliveryPartner && (
                        <TouchableOpacity
                            className={`px-4 py-2 rounded-full flex-row items-center ${currentDeliveryPartner.isAvailable ? 'bg-green-500/90' : 'bg-gray-400/90'}`}
                            style={SHADOWS.small}
                            onPress={async () => {
                                try {
                                    setRefreshing(true);
                                    // Use the toggleAvailability function from the context
                                    const partnerId = currentDeliveryPartner._id || currentDeliveryPartner.id;
                                    await toggleAvailability(partnerId);
                                    // Refresh orders after changing availability
                                    fetchOrders();
                                } catch (error) {
                                    console.error('Error updating availability:', error);
                                    showCustomAlert("Error", "Failed to update availability status.", "error");
                                } finally {
                                    setRefreshing(false);
                                }
                            }}
                        >
                            <View className={`w-3 h-3 rounded-full mr-2 ${currentDeliveryPartner.isAvailable ? 'bg-white' : 'bg-gray-200'}`} />
                            <Text className="text-white font-medium">
                                {currentDeliveryPartner.isAvailable ? 'Available' : 'Unavailable'}
                            </Text>
                        </TouchableOpacity>
                    )}
                </View>
            </LinearGradient>



            {/* Status Filter */}
            <View className="mx-4 mb-4">
                <Text className="text-lg font-bold text-gray-800 mb-2">Order Status</Text>
                <View className="flex-row bg-white rounded-xl p-1.5 shadow-sm">
                    <TouchableOpacity
                        className={`flex-1 py-2 mx-1 rounded-lg ${selectedStatus === 'all' ? 'bg-madder' : 'bg-gray-100'}`}
                        onPress={() => setSelectedStatus('all')}
                    >
                        <Text className={`text-center font-medium ${selectedStatus === 'all' ? 'text-white' : 'text-gray-600'}`}>All</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        className={`flex-1 py-2 mx-1 rounded-lg ${selectedStatus === 'pending' ? 'bg-madder' : 'bg-gray-100'}`}
                        onPress={() => setSelectedStatus('pending')}
                    >
                        <Text className={`text-center font-medium ${selectedStatus === 'pending' ? 'text-white' : 'text-gray-600'}`}>Pending</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        className={`flex-1 py-2 mx-1 rounded-lg ${selectedStatus === 'in-transit' ? 'bg-madder' : 'bg-gray-100'}`}
                        onPress={() => setSelectedStatus('in-transit')}
                    >
                        <Text className={`text-center font-medium ${selectedStatus === 'in-transit' ? 'text-white' : 'text-gray-600'}`}>In Transit</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        className={`flex-1 py-2 mx-1 rounded-lg ${selectedStatus === 'delivered' ? 'bg-madder' : 'bg-gray-100'}`}
                        onPress={() => setSelectedStatus('delivered')}
                    >
                        <Text className={`text-center font-medium ${selectedStatus === 'delivered' ? 'text-white' : 'text-gray-600'}`}>Delivered</Text>
                    </TouchableOpacity>
                </View>
            </View>

            <FlatList
                className="p-4 pt-0"
                data={filteredOrders}
                keyExtractor={item => item.id}
                renderItem={renderOrderItem}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{ paddingBottom: 100 }}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={fetchOrders}
                        colors={['#A31621']}
                        tintColor="#A31621"
                    />
                }
                ListEmptyComponent={
                    <View className="items-center justify-center py-10 px-6">
                        <View className="bg-gray-100 p-4 rounded-full mb-4">
                            <MaterialIcons name="inbox" size={48} color={COLORS.primary} />
                        </View>
                        <Text className="text-gray-700 text-xl font-bold mt-2">No orders found</Text>
                        <Text className="text-gray-500 text-center mt-2 leading-5">
                            {selectedStatus === 'all'
                                ? "You don't have any orders yet"
                                : `No ${selectedStatus.replace('-', ' ')} orders at the moment`}
                        </Text>

                        {/* Status-specific messages */}
                        {selectedStatus === 'pending' && !currentDeliveryPartner?.isAvailable && (
                            <Text className="text-gray-500 text-center mt-2 leading-5">
                                Set your status to Available to see pending orders
                            </Text>
                        )}

                        {/* Action buttons */}
                        <View className="flex-row mt-6">
                            {!currentDeliveryPartner?.isAvailable && (
                                <TouchableOpacity
                                    className="bg-madder px-6 py-3 rounded-xl flex-row items-center mx-2"
                                    style={SHADOWS.medium}
                                    onPress={async () => {
                                        try {
                                            const partnerId = currentDeliveryPartner._id || currentDeliveryPartner.id;
                                            if (partnerId) {
                                                await toggleAvailability(partnerId);
                                                fetchOrders();
                                            } else {
                                                showCustomAlert("Error", "Could not determine delivery partner ID.", "error");
                                            }
                                        } catch (error) {
                                            console.error('Error updating availability:', error);
                                            showCustomAlert("Error", "Failed to update availability status.", "error");
                                        }
                                    }}
                                >
                                    <MaterialIcons name="check-circle" size={20} color="white" />
                                    <Text className="text-white font-bold ml-2">Set Available</Text>
                                </TouchableOpacity>
                            )}

                            <TouchableOpacity
                                className={`${!currentDeliveryPartner?.isAvailable ? 'bg-madder/10' : 'bg-madder'} px-6 py-3 rounded-xl flex-row items-center mx-2`}
                                style={!currentDeliveryPartner?.isAvailable ? {} : SHADOWS.medium}
                                onPress={fetchOrders}
                            >
                                <MaterialIcons
                                    name="refresh"
                                    size={20}
                                    color={!currentDeliveryPartner?.isAvailable ? COLORS.primary : "white"}
                                />
                                <Text className={`${!currentDeliveryPartner?.isAvailable ? 'text-madder' : 'text-white'} font-bold ml-2`}>
                                    Refresh
                                </Text>
                            </TouchableOpacity>
                        </View>

                        {/* Partner status info */}
                        <View className="bg-white p-4 rounded-xl mt-6 w-full shadow-sm">
                            <View className="flex-row items-center mb-2">
                                <View className={`w-3 h-3 rounded-full mr-2 ${currentDeliveryPartner?.isAvailable ? 'bg-green-500' : 'bg-gray-400'}`} />
                                <Text className="text-gray-800 font-bold">
                                    Status: {currentDeliveryPartner?.isAvailable ? 'Available' : 'Unavailable'}
                                </Text>
                            </View>
                            <Text className="text-gray-600 mt-1">
                                {currentDeliveryPartner?.isAvailable
                                    ? 'You are available to receive orders. Check the "Pending" tab to see unassigned orders you can pick up.'
                                    : 'You are currently unavailable. Set status to Available to see new orders.'}
                            </Text>
                        </View>
                    </View>
                }
            />

            {/* Custom Alert Modal */}
            <CustomAlertModal
                visible={alertVisible}
                title={alertTitle}
                message={alertMessage}
                icon={alertIcon}
                buttons={alertButtons}
                onClose={() => setAlertVisible(false)}
            />

            {/* Success Toast Modal */}
            {showSuccessModal && (
                <View className="absolute bottom-10 left-0 right-0 mx-4">
                    <Animated.View
                        className="bg-green-600 rounded-xl p-4 shadow-lg"
                        style={SHADOWS.large}
                    >
                        <View className="flex-row items-center">
                            <View className="bg-white/20 p-2 rounded-full mr-3">
                                <MaterialIcons name="check-circle" size={24} color="white" />
                            </View>
                            <Text className="text-white font-bold flex-1">{successMessage}</Text>
                        </View>
                    </Animated.View>
                </View>
            )}
        </View>
    );
};

export default DeliveryPartnerOrdersScreen;
