import React, { useState, useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, ScrollView, Switch, Alert, RefreshControl, Modal, Animated } from 'react-native';
import { MaterialIcons } from "@expo/vector-icons";
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import { useDeliveryPartner } from '../../context/DeliveryPartnerContext';
import { useAuth } from '../../context/AuthContext';
import { getDeliveryPartnerProfile } from '../../utils/api/deliveryApi';
import { COLORS, SHADOWS, CARD_STYLES, BUTTON_STYLES, TEXT_STYLES } from '../../styles/deliveryTheme';

const DeliveryPartnerProfileScreen = () => {
    const navigation = useNavigation();
    const { currentDeliveryPartner, toggleAvailability, setCurrentDeliveryPartner } = useDeliveryPartner();
    const { logout: authLogout } = useAuth();
    const [refreshing, setRefreshing] = useState(false);
    const [showLogoutModal, setShowLogoutModal] = useState(false);
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const scaleAnim = useRef(new Animated.Value(0.8)).current;

    // Use profile data from context, but also fetch directly from API
    const [profile, setProfile] = useState(currentDeliveryPartner || {});

    // Fetch profile data only on component mount
    useEffect(() => {
        console.log('ProfileScreen: Component mounted');
        if (currentDeliveryPartner) {
            console.log('ProfileScreen: Setting profile from currentDeliveryPartner');
            setProfile(currentDeliveryPartner);
            fetchProfileData();
        } else {
            console.log('ProfileScreen: No delivery partner available');
            setProfile({});
        }
    }, []); // Empty dependency array means this only runs once on mount

    // Add a focus listener to refresh data when tab is focused
    useEffect(() => {
        const unsubscribe = navigation.addListener('focus', () => {
            console.log('ProfileScreen: Screen focused, refreshing data');
            // Only fetch if not already refreshing and we have a delivery partner
            if (currentDeliveryPartner && !refreshing) {
                fetchProfileData();
            }
        });

        // Cleanup the listener on unmount
        return unsubscribe;
    }, [navigation, currentDeliveryPartner, refreshing]);

    // Fetch profile data from API
    const fetchProfileData = async (retryCount = 0) => {
        try {
            // We can't call hooks inside functions, so we'll use the currentDeliveryPartner from props
            if (!currentDeliveryPartner) {
                console.log('ProfileScreen: No delivery partner available, skipping profile fetch');
                setRefreshing(false);
                return;
            }

            setRefreshing(true);
            console.log('ProfileScreen: Fetching profile data from API...');
            console.log('ProfileScreen: Current delivery partner:', currentDeliveryPartner);

            const response = await getDeliveryPartnerProfile();
            console.log('ProfileScreen: Profile data received from API:', response);

            if (response) {
                // Merge the response with any existing data to ensure we have all fields
                const mergedProfile = {
                    ...currentDeliveryPartner,
                    ...response,
                    // Ensure these critical fields are set
                    name: response.name || currentDeliveryPartner.name || 'Delivery Partner',
                    phoneNumber: response.phoneNumber || response.phone || currentDeliveryPartner.phoneNumber || currentDeliveryPartner.phone,
                    isAvailable: response.isAvailable !== undefined ? response.isAvailable :
                                (currentDeliveryPartner.isAvailable !== undefined ? currentDeliveryPartner.isAvailable : true)
                };

                console.log('ProfileScreen: Merged profile data:', mergedProfile);

                // Update only local state, not context
                setProfile(mergedProfile);

                // Don't update the context here to avoid infinite loops
                // setCurrentDeliveryPartner(mergedProfile);

                console.log('ProfileScreen: Profile updated successfully');
            } else {
                console.error('ProfileScreen: Empty response from API');
                throw new Error('Empty response from server');
            }
        } catch (error) {
            console.error('ProfileScreen: Error fetching profile data:', error);

            // Retry logic (up to 1 retry to prevent infinite loops)
            if (retryCount < 1) {
                console.log(`ProfileScreen: Retrying fetch profile (attempt ${retryCount + 1})...`);
                setTimeout(() => fetchProfileData(retryCount + 1), 2000);
                return;
            }

            // If API fails after retries, use data from context
            if (currentDeliveryPartner) {
                console.log('ProfileScreen: Falling back to context data');
                setProfile(currentDeliveryPartner);
            }

            // Only show error alert after retries fail
            Alert.alert(
                "Error",
                "Failed to fetch profile data. Using cached data instead.",
                [{ text: "Retry", onPress: () => fetchProfileData(0) }]
            );
        } finally {
            setRefreshing(false);
        }
    };

    const handleLogout = () => {
        setShowLogoutModal(true);

        // Animate the modal appearance
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 300,
                useNativeDriver: true,
            }),
            Animated.spring(scaleAnim, {
                toValue: 1,
                friction: 8,
                tension: 40,
                useNativeDriver: true,
            })
        ]).start();
    };

    const confirmLogout = async () => {
        try {
            // Call the auth logout function
            await authLogout();
            console.log('Auth logout successful for delivery partner');

            // Reset navigation to login screen
            navigation.reset({
                index: 0,
                routes: [{ name: 'PreLoginScreen' }],
            });
        } catch (error) {
            console.error('Error during logout:', error);
            // Still navigate to login screen even if there's an error
            navigation.reset({
                index: 0,
                routes: [{ name: 'PreLoginScreen' }],
            });
        }
    };

    const cancelLogout = () => {
        // Animate the modal disappearance
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 0,
                duration: 200,
                useNativeDriver: true,
            }),
            Animated.timing(scaleAnim, {
                toValue: 0.8,
                duration: 200,
                useNativeDriver: true,
            })
        ]).start(() => {
            setShowLogoutModal(false);
        });
    };

    // Get in-transit orders count from the profile data

    const handleToggleAvailability = async () => {
        // Get the current availability status from either profile or currentDeliveryPartner
        const isCurrentlyAvailable = profile?.isAvailable === true || currentDeliveryPartner?.isAvailable === true;

        // If currently available and trying to go unavailable while having in-transit orders
        const inTransitOrders = profile?.inTransitOrders || currentDeliveryPartner?.inTransitOrders || 0;

        if (isCurrentlyAvailable && inTransitOrders > 0) {
            // Create a custom alert for in-transit orders
            Alert.alert(
                "Cannot Change Status",
                `You have ${inTransitOrders} order(s) in transit. Please complete all deliveries before going offline.`,
                [{
                    text: "OK",
                    style: "cancel"
                }]
            );
            return;
        }

        // Get the ID from either profile or currentDeliveryPartner
        const partnerId = profile?.id || profile?._id || currentDeliveryPartner?.id || currentDeliveryPartner?._id;

        // Make sure we have a valid ID before toggling
        if (partnerId) {
            console.log('Toggling availability for partner ID:', partnerId);
            console.log('Current availability status:', isCurrentlyAvailable);
            console.log('Setting availability to:', !isCurrentlyAvailable);

            try {
                // First, show a loading state in the UI
                setRefreshing(true);

                // Call the toggle function from context
                // This will update the backend and the context state
                await toggleAvailability(partnerId);

                // Refresh the profile data to ensure UI and backend are in sync
                await fetchProfileData();

                // Update local state to reflect the change
                const newAvailability = !isCurrentlyAvailable;
                setProfile(prevProfile => ({
                    ...prevProfile,
                    isAvailable: newAvailability
                }));

                // Also update the currentDeliveryPartner in the context
                if (currentDeliveryPartner) {
                    setCurrentDeliveryPartner(prev => ({
                        ...prev,
                        isAvailable: newAvailability
                    }));
                }

                // Show success message
                const newStatus = newAvailability ? "Available" : "Unavailable";

                // Create a custom alert for status update
                Alert.alert(
                    "Status Updated",
                    `You are now ${newStatus} for deliveries.`,
                    [{ text: "OK" }]
                );

                console.log('Availability toggled successfully to:', newAvailability);
            } catch (error) {
                console.error('Error toggling availability:', error);

                // Log detailed error information
                if (error.response) {
                    console.error('Error response:', {
                        status: error.response.status,
                        data: error.response.data
                    });
                }

                Alert.alert(
                    "Error",
                    "Failed to update availability status. Please try again.",
                    [{ text: "OK" }]
                );
            } finally {
                // End the loading state
                setRefreshing(false);
            }
        } else {
            console.error('No valid ID found for delivery partner');
            Alert.alert(
                "Error",
                "Could not update availability status. Please try again later.",
                [{ text: "OK" }]
            );
        }
    };

    return (
        <View className="flex-1 bg-snow">
            <LinearGradient
                colors={[COLORS.primaryDark, COLORS.primary]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                className="p-4 pt-12 pb-4 rounded-b-3xl"
            >
                <View className="flex-row items-center">
                    <MaterialIcons name="account-circle" size={22} color="white" />
                    <Text className="text-lg text-white font-bold ml-2">Profile</Text>
                </View>
            </LinearGradient>

            <ScrollView
                className="p-4"
                contentContainerStyle={{ paddingBottom: 100 }}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={fetchProfileData}
                        colors={['#A31621']}
                        tintColor="#A31621"
                    />
                }>
                {/* Profile Card */}
                <View className="bg-white rounded-xl p-5 mb-5" style={SHADOWS.medium}>
                    <View className="flex-row items-center mb-4">
                        <View className="w-20 h-20 rounded-full bg-madder/10 items-center justify-center mr-4">
                            <MaterialIcons name="person" size={40} color={COLORS.primary} />
                        </View>
                        <View className="flex-1">
                            <Text className="text-xl font-bold text-gray-800">{profile?.name || currentDeliveryPartner?.name || 'Delivery Partner'}</Text>
                            <View className="flex-row items-center mt-1">
                                <MaterialIcons name="phone" size={16} color={COLORS.darkGray} />
                                <Text className="ml-2 text-gray-600">{profile?.phoneNumber || profile?.phone || currentDeliveryPartner?.phoneNumber || currentDeliveryPartner?.phone || 'No phone number'}</Text>
                            </View>
                        </View>
                    </View>

                    <View className="bg-gray-50 p-4 rounded-xl mt-2">
                        <View className="flex-row justify-between items-center">
                            <View className="flex-row items-center">
                                <MaterialIcons
                                    name={profile?.isAvailable === true || currentDeliveryPartner?.isAvailable === true ? "check-circle" : "cancel"}
                                    size={20}
                                    color={profile?.isAvailable === true || currentDeliveryPartner?.isAvailable === true ? COLORS.success : COLORS.mediumGray}
                                />
                                <Text className="ml-2 font-medium text-gray-800">Available for Delivery</Text>
                            </View>
                            <Switch
                                trackColor={{ false: "#E5E7EB", true: COLORS.primary }}
                                thumbColor="#FFFFFF"
                                ios_backgroundColor="#E5E7EB"
                                onValueChange={handleToggleAvailability}
                                value={profile?.isAvailable === true || currentDeliveryPartner?.isAvailable === true}
                            />
                        </View>
                    </View>
                </View>



                {/* Vehicle Information */}
                <View className="bg-white rounded-xl p-5 mb-5" style={SHADOWS.medium}>
                    <View className="flex-row items-center mb-3">
                        <MaterialIcons name="directions-bike" size={22} color={COLORS.primary} />
                        <Text className="text-lg font-bold ml-2 text-gray-800">Vehicle Information</Text>
                    </View>

                    <View className="bg-gray-50 p-4 rounded-xl">
                        <View className="flex-row items-center mb-3">
                            <View className="w-10 h-10 rounded-full bg-madder/10 items-center justify-center">
                                <MaterialIcons name="two-wheeler" size={20} color={COLORS.primary} />
                            </View>
                            <View className="ml-3">
                                <Text className="text-gray-500 text-xs">Vehicle Type</Text>
                                <Text className="text-gray-800 font-medium">{profile.vehicleType || 'Two Wheeler'}</Text>
                            </View>
                        </View>

                        <View className="flex-row items-center">
                            <View className="w-10 h-10 rounded-full bg-madder/10 items-center justify-center">
                                <MaterialIcons name="credit-card" size={20} color={COLORS.primary} />
                            </View>
                            <View className="ml-3">
                                <Text className="text-gray-500 text-xs">Vehicle Number</Text>
                                <Text className="text-gray-800 font-medium">{profile.vehicleNumber || 'Not specified'}</Text>
                            </View>
                        </View>
                    </View>
                </View>

                {/* Account Settings */}
                <View className="bg-white rounded-xl p-5 mb-5" style={SHADOWS.medium}>
                    <View className="flex-row items-center mb-3">
                        <MaterialIcons name="settings" size={22} color={COLORS.primary} />
                        <Text className="text-lg font-bold ml-2 text-gray-800">Account Settings</Text>
                    </View>

                    <View className="bg-gray-50 rounded-xl overflow-hidden">
                        <TouchableOpacity
                            className="flex-row items-center p-4 active:bg-gray-100"
                            onPress={() => navigation.navigate('DeliveryHelpSupportScreen')}
                        >
                            <View className="w-10 h-10 rounded-full bg-madder/10 items-center justify-center">
                                <MaterialIcons name="help-outline" size={20} color={COLORS.primary} />
                            </View>
                            <Text className="ml-3 text-gray-700 font-medium flex-1">Help & Support</Text>
                            <MaterialIcons name="chevron-right" size={20} color={COLORS.darkGray} />
                        </TouchableOpacity>
                    </View>
                </View>

                {/* Logout Button */}
                <TouchableOpacity
                    className="bg-white rounded-xl p-4 mb-5 flex-row items-center justify-center"
                    style={SHADOWS.small}
                    onPress={handleLogout}
                    activeOpacity={0.7}
                >
                    <MaterialIcons name="logout" size={20} color={COLORS.primary} />
                    <Text className="ml-2 text-madder font-medium">Logout</Text>
                </TouchableOpacity>


            </ScrollView>

            {/* Logout Modal */}
            <Modal
                visible={showLogoutModal}
                transparent={true}
                animationType="none"
                onRequestClose={cancelLogout}
            >
                <Animated.View
                    className="flex-1 justify-center items-center bg-black/30"
                    style={{ opacity: fadeAnim }}
                >
                    <Animated.View
                        className="bg-white rounded-2xl p-6 m-4 items-center"
                        style={{
                            transform: [{ scale: scaleAnim }],
                            width: '80%',
                            maxWidth: 320
                        }}
                    >
                        <View className="w-16 h-16 bg-madder/10 rounded-full items-center justify-center mb-4">
                            <MaterialIcons name="logout" size={32} color={COLORS.primary} />
                        </View>
                        <Text className="text-xl font-bold text-gray-800 mb-2">Logout</Text>
                        <Text className="text-gray-600 text-center mb-6">Are you sure you want to logout from your account?</Text>

                        <View className="flex-row w-full">
                            <TouchableOpacity
                                className="flex-1 bg-gray-200 py-3 rounded-xl mr-2 items-center"
                                onPress={cancelLogout}
                            >
                                <Text className="font-medium text-gray-700">Cancel</Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                className="flex-1 bg-madder py-3 rounded-xl ml-2 items-center"
                                onPress={confirmLogout}
                            >
                                <Text className="font-medium text-white">Logout</Text>
                            </TouchableOpacity>
                        </View>
                    </Animated.View>
                </Animated.View>
            </Modal>
        </View>
    );
};

export default DeliveryPartnerProfileScreen;
