import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Switch, ScrollView, Alert, RefreshControl } from 'react-native';
import { MaterialIcons } from "@expo/vector-icons";
import { useNavigation } from '@react-navigation/native';
import { useDeliveryPartner } from '../../context/DeliveryPartnerContext';

const NotificationsSettingsScreen = () => {
    const navigation = useNavigation();
    const { currentDeliveryPartner, setCurrentDeliveryPartner } = useDeliveryPartner();
    const [refreshing, setRefreshing] = useState(false);

    // Initialize with default values
    const [notifications, setNotifications] = useState({
        newOrders: true,
        orderUpdates: true,
        deliveryReminders: true,
        paymentAlerts: true,
        promotions: false,
        appUpdates: true,
    });

    // Load notification settings from delivery partner profile
    useEffect(() => {
        if (currentDeliveryPartner && currentDeliveryPartner.notificationPreferences) {
            setNotifications({
                ...notifications,
                ...currentDeliveryPartner.notificationPreferences
            });
        }
    }, [currentDeliveryPartner]);

    const toggleSwitch = (key) => {
        setNotifications(prev => ({
            ...prev,
            [key]: !prev[key]
        }));
    };

    const handleRefresh = () => {
        setRefreshing(true);
        // Reload notification settings from profile
        if (currentDeliveryPartner && currentDeliveryPartner.notificationPreferences) {
            setNotifications({
                ...notifications,
                ...currentDeliveryPartner.notificationPreferences
            });
        }
        setRefreshing(false);
    };

    const handleSave = async () => {
        try {
            setRefreshing(true);

            // Since we can't update the profile directly anymore, we'll just update the local state
            // In a real app, this would be handled by the backend

            // Update the local state
            if (currentDeliveryPartner) {
                const updatedPartner = {
                    ...currentDeliveryPartner,
                    notificationPreferences: notifications
                };

                // Update the context
                setCurrentDeliveryPartner(updatedPartner);
            }

            Alert.alert(
                "Success",
                "Notification settings saved locally. These will reset when you log out.",
                [{ text: "OK", onPress: () => navigation.goBack() }]
            );
        } catch (error) {
            console.error('Error saving notification settings:', error);
            Alert.alert(
                "Error",
                "Failed to save notification settings. Please try again.",
                [{ text: "OK" }]
            );
        } finally {
            setRefreshing(false);
        }
    };

    return (
        <View className="flex-1 bg-snow">
            <View className="bg-madder p-6 pt-16 pb-6 flex-row items-center">
                <TouchableOpacity onPress={() => navigation.goBack()} className="mr-4">
                    <MaterialIcons name="arrow-back" size={24} color="white" />
                </TouchableOpacity>
                <Text className="text-xl text-white font-bold">Notification Settings</Text>
            </View>

            <ScrollView
                className="p-4"
                contentContainerStyle={{ paddingBottom: 100 }}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={handleRefresh}
                        colors={['#A31621']}
                        tintColor="#A31621"
                    />
                }>
                <View className="bg-white rounded-xl p-4 mb-4 shadow-sm">
                    <Text className="text-lg font-bold mb-3">Push Notifications</Text>

                    <View className="flex-row justify-between items-center py-3 border-b border-gray-100">
                        <View className="flex-1 mr-4">
                            <Text className="text-gray-800 font-medium">New Orders</Text>
                            <Text className="text-gray-500 text-xs">Get notified when new orders are assigned to you</Text>
                        </View>
                        <Switch
                            trackColor={{ false: "#E5E7EB", true: "#A31621" }}
                            thumbColor="#FFFFFF"
                            ios_backgroundColor="#E5E7EB"
                            onValueChange={() => toggleSwitch('newOrders')}
                            value={notifications.newOrders}
                        />
                    </View>

                    <View className="flex-row justify-between items-center py-3 border-b border-gray-100">
                        <View className="flex-1 mr-4">
                            <Text className="text-gray-800 font-medium">Order Updates</Text>
                            <Text className="text-gray-500 text-xs">Receive updates about your ongoing orders</Text>
                        </View>
                        <Switch
                            trackColor={{ false: "#E5E7EB", true: "#A31621" }}
                            thumbColor="#FFFFFF"
                            ios_backgroundColor="#E5E7EB"
                            onValueChange={() => toggleSwitch('orderUpdates')}
                            value={notifications.orderUpdates}
                        />
                    </View>

                    <View className="flex-row justify-between items-center py-3 border-b border-gray-100">
                        <View className="flex-1 mr-4">
                            <Text className="text-gray-800 font-medium">Delivery Reminders</Text>
                            <Text className="text-gray-500 text-xs">Get reminders about upcoming deliveries</Text>
                        </View>
                        <Switch
                            trackColor={{ false: "#E5E7EB", true: "#A31621" }}
                            thumbColor="#FFFFFF"
                            ios_backgroundColor="#E5E7EB"
                            onValueChange={() => toggleSwitch('deliveryReminders')}
                            value={notifications.deliveryReminders}
                        />
                    </View>

                    <View className="flex-row justify-between items-center py-3 border-b border-gray-100">
                        <View className="flex-1 mr-4">
                            <Text className="text-gray-800 font-medium">Payment Alerts</Text>
                            <Text className="text-gray-500 text-xs">Get notified about payments and earnings</Text>
                        </View>
                        <Switch
                            trackColor={{ false: "#E5E7EB", true: "#A31621" }}
                            thumbColor="#FFFFFF"
                            ios_backgroundColor="#E5E7EB"
                            onValueChange={() => toggleSwitch('paymentAlerts')}
                            value={notifications.paymentAlerts}
                        />
                    </View>

                    <View className="flex-row justify-between items-center py-3 border-b border-gray-100">
                        <View className="flex-1 mr-4">
                            <Text className="text-gray-800 font-medium">Promotions & Offers</Text>
                            <Text className="text-gray-500 text-xs">Receive promotional messages and special offers</Text>
                        </View>
                        <Switch
                            trackColor={{ false: "#E5E7EB", true: "#A31621" }}
                            thumbColor="#FFFFFF"
                            ios_backgroundColor="#E5E7EB"
                            onValueChange={() => toggleSwitch('promotions')}
                            value={notifications.promotions}
                        />
                    </View>

                    <View className="flex-row justify-between items-center py-3">
                        <View className="flex-1 mr-4">
                            <Text className="text-gray-800 font-medium">App Updates</Text>
                            <Text className="text-gray-500 text-xs">Get notified about app updates and new features</Text>
                        </View>
                        <Switch
                            trackColor={{ false: "#E5E7EB", true: "#A31621" }}
                            thumbColor="#FFFFFF"
                            ios_backgroundColor="#E5E7EB"
                            onValueChange={() => toggleSwitch('appUpdates')}
                            value={notifications.appUpdates}
                        />
                    </View>
                </View>

                <TouchableOpacity
                    className="bg-madder py-4 rounded-xl items-center mb-4"
                    onPress={handleSave}
                >
                    <Text className="text-white font-bold">Save Settings</Text>
                </TouchableOpacity>
            </ScrollView>
        </View>
    );
};

export default NotificationsSettingsScreen;
