import axios from 'axios';
import { API_URL, USER_TYPES } from '../../config/constants';
import { getAuthToken, getUserData } from '../authStorage';

// Get all categories
export const getAllCategories = async () => {
    try {
        const response = await axios.get(`${API_URL}/categories`, {
            timeout: 10000 // 10 second timeout
        });

        console.log('Categories API response status:', response.status);
        console.log('Categories found:', response.data.length);

        return response.data;
    } catch (error) {
        console.error('Error fetching categories:', error);
        console.error('Error details:', {
            message: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            url: error.config?.url
        });
        throw error;
    }
};

// Get category by ID
export const getCategoryById = async (categoryId) => {
    try {
        const response = await axios.get(`${API_URL}/categories/${categoryId}`, {
            timeout: 10000 // 10 second timeout
        });

        return response.data;
    } catch (error) {
        console.error(`Error fetching category ${categoryId}:`, error);
        throw error;
    }
};

// Create category (admin only)
export const createCategory = async (categoryData) => {
    try {
        const token = await getAuthToken();

        // Get user data to check user type
        const userData = await getUserData();
        const userType = userData?.userType?.toUpperCase();

        console.log('CategoryAPI: User type for createCategory:', userType);

        // Only admins should be able to create categories
        if (userType !== USER_TYPES.ADMIN) {
            console.error('Unauthorized: Only admins can create categories');
            throw new Error('Unauthorized: Admin access required');
        }

        const response = await axios.post(`${API_URL}/categories`, categoryData, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        return response.data;
    } catch (error) {
        console.error('Error creating category:', error);
        throw error;
    }
};

// Update category (admin only)
export const updateCategory = async (categoryId, categoryData) => {
    try {
        const token = await getAuthToken();

        // Get user data to check user type
        const userData = await getUserData();
        const userType = userData?.userType?.toUpperCase();

        console.log('CategoryAPI: User type for updateCategory:', userType);

        // Only admins should be able to update categories
        if (userType !== USER_TYPES.ADMIN) {
            console.error('Unauthorized: Only admins can update categories');
            throw new Error('Unauthorized: Admin access required');
        }

        const response = await axios.put(`${API_URL}/categories/${categoryId}`, categoryData, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        return response.data;
    } catch (error) {
        console.error(`Error updating category ${categoryId}:`, error);
        throw error;
    }
};

// Delete category (admin only)
export const deleteCategory = async (categoryId) => {
    try {
        const token = await getAuthToken();

        // Get user data to check user type
        const userData = await getUserData();
        const userType = userData?.userType?.toUpperCase();

        console.log('CategoryAPI: User type for deleteCategory:', userType);

        // Only admins should be able to delete categories
        if (userType !== USER_TYPES.ADMIN) {
            console.error('Unauthorized: Only admins can delete categories');
            throw new Error('Unauthorized: Admin access required');
        }

        const response = await axios.delete(`${API_URL}/categories/${categoryId}`, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });

        return response.data;
    } catch (error) {
        console.error(`Error deleting category ${categoryId}:`, error);
        throw error;
    }
};
