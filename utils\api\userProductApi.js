import axios from 'axios';
import { API_URL } from '../../config/constants';
import { getAuthToken } from '../authStorage';

// Get all products for user
export const getUserProducts = async () => {
    try {
        const token = await getAuthToken();
        const response = await axios.get(`${API_URL}/products`, {
            headers: {
                Authorization: `Bearer ${token}`
            },
            timeout: 10000 // 10 second timeout
        });
        
        return response.data;
    } catch (error) {
        console.error('Error fetching products:', error);
        throw error;
    }
};

// Get product by ID for user
export const getUserProductById = async (productId) => {
    try {
        if (!productId) {
            console.error('Invalid product ID provided:', productId);
            throw new Error('Invalid product ID provided');
        }

        const token = await getAuthToken();
        const response = await axios.get(`${API_URL}/products/${productId}`, {
            headers: {
                Authorization: `Bearer ${token}`
            },
            timeout: 10000 // 10 second timeout
        });

        if (response.data && response.data.product) {
            return response.data;
        } else {
            throw new Error('Product data not found in API response');
        }
    } catch (error) {
        console.error(`Error fetching product ${productId}:`, error);
        throw error;
    }
};

// Get products by category for user
export const getUserProductsByCategory = async (categoryId) => {
    try {
        if (!categoryId) {
            console.error('Invalid category ID provided:', categoryId);
            throw new Error('Invalid category ID provided');
        }

        const token = await getAuthToken();
        const response = await axios.get(`${API_URL}/products/category/${categoryId}`, {
            headers: {
                Authorization: `Bearer ${token}`
            },
            timeout: 10000 // 10 second timeout
        });

        return response.data;
    } catch (error) {
        console.error(`Error fetching products for category ${categoryId}:`, error);
        throw error;
    }
};

// Get bestselling products for user
export const getBestsellingProducts = async () => {
    try {
        const token = await getAuthToken();
        const response = await axios.get(`${API_URL}/products/bestselling`, {
            headers: {
                Authorization: `Bearer ${token}`
            },
            timeout: 10000 // 10 second timeout
        });

        return response.data;
    } catch (error) {
        console.error('Error fetching bestselling products:', error);
        throw error;
    }
};
